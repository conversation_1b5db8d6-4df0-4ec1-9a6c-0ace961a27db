# 📘 Module 11: Network and Data Transfer Costs

## 🎯 Learning Goals
By the end of this module, you will:
- Optimize NAT Gateway costs and implement alternatives
- Leverage VPC Endpoints to reduce data transfer costs
- Implement CloudFront and CDN optimization strategies
- Monitor and analyze network traffic patterns
- Use both free and premium tools for network cost optimization

## 📚 Table of Contents
1. [NAT Gateway Cost Optimization](#nat-gateway-cost-optimization)
2. [VPC Endpoints vs NAT Gateway](#vpc-endpoints-vs-nat-gateway)
3. [CloudFront and CDN Optimization](#cloudfront-and-cdn-optimization)
4. [Network Traffic Analysis](#network-traffic-analysis)
5. [Tools and Integration](#tools-and-integration)
6. [Study Checkpoints](#study-checkpoints)
7. [Practical Examples](#practical-examples)

## 🌐 NAT Gateway Cost Optimization

### Understanding NAT Gateway Costs
```yaml
nat_gateway_pricing:
  hourly_cost: "$0.045/hour"  # ~$32.40/month per NAT Gateway
  data_processing: "$0.045/GB"  # Per GB processed
  
  cost_factors:
    - "Number of NAT Gateways"
    - "Data processing volume"
    - "Cross-AZ data transfer"
    - "Internet data transfer"
    
  optimization_strategies:
    - "Reduce number of NAT Gateways"
    - "Use VPC Endpoints for AWS services"
    - "Implement NAT instances for low-traffic scenarios"
    - "Optimize application data transfer patterns"
```

### NAT Gateway Alternatives
```python
import boto3
import json
from datetime import datetime, timedelta
from typing import Dict, List

class NATGatewayOptimizer:
    def __init__(self):
        self.ec2 = boto3.client('ec2')
        self.cloudwatch = boto3.client('cloudwatch')
        self.ce = boto3.client('ce')
        
    def analyze_nat_gateway_usage(self, days: int = 30) -> List[Dict]:
        """Analyze NAT Gateway usage and costs"""
        nat_gateways = self.ec2.describe_nat_gateways()
        analysis_results = []
        
        for nat_gw in nat_gateways['NatGateways']:
            if nat_gw['State'] != 'available':
                continue
                
            nat_gw_id = nat_gw['NatGatewayId']
            subnet_id = nat_gw['SubnetId']
            
            # Get usage metrics
            usage_metrics = self._get_nat_gateway_metrics(nat_gw_id, days)
            
            # Calculate costs
            cost_analysis = self._calculate_nat_gateway_costs(usage_metrics, days)
            
            # Generate optimization recommendations
            recommendations = self._generate_nat_optimization_recommendations(
                nat_gw, usage_metrics, cost_analysis
            )
            
            analysis_results.append({
                'nat_gateway_id': nat_gw_id,
                'subnet_id': subnet_id,
                'availability_zone': nat_gw.get('AvailabilityZone'),
                'usage_metrics': usage_metrics,
                'cost_analysis': cost_analysis,
                'recommendations': recommendations
            })
        
        return analysis_results
    
    def _get_nat_gateway_metrics(self, nat_gw_id: str, days: int) -> Dict:
        """Get CloudWatch metrics for NAT Gateway"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)
        
        metrics = {}
        
        try:
            # Bytes processed
            bytes_out = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/NATGateway',
                MetricName='BytesOutToDestination',
                Dimensions=[{'Name': 'NatGatewayId', 'Value': nat_gw_id}],
                StartTime=start_time,
                EndTime=end_time,
                Period=3600,
                Statistics=['Sum']
            )
            
            bytes_in = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/NATGateway',
                MetricName='BytesInFromDestination',
                Dimensions=[{'Name': 'NatGatewayId', 'Value': nat_gw_id}],
                StartTime=start_time,
                EndTime=end_time,
                Period=3600,
                Statistics=['Sum']
            )
            
            # Calculate totals
            total_bytes_out = sum(dp['Sum'] for dp in bytes_out['Datapoints'])
            total_bytes_in = sum(dp['Sum'] for dp in bytes_in['Datapoints'])
            total_bytes = total_bytes_out + total_bytes_in
            
            # Calculate daily averages
            daily_avg_gb = (total_bytes / (1024**3)) / days
            
            metrics = {
                'total_bytes_processed': total_bytes,
                'total_gb_processed': round(total_bytes / (1024**3), 2),
                'daily_average_gb': round(daily_avg_gb, 2),
                'bytes_out': total_bytes_out,
                'bytes_in': total_bytes_in
            }
            
        except Exception as e:
            metrics = {'error': f'Error getting metrics: {str(e)}'}
        
        return metrics
    
    def _calculate_nat_gateway_costs(self, usage_metrics: Dict, days: int) -> Dict:
        """Calculate NAT Gateway costs"""
        if 'error' in usage_metrics:
            return {'error': 'Cannot calculate costs due to metrics error'}
        
        # NAT Gateway pricing (simplified - varies by region)
        hourly_rate = 0.045  # $0.045/hour
        data_processing_rate = 0.045  # $0.045/GB
        
        # Calculate costs
        hours_in_period = days * 24
        hourly_cost = hours_in_period * hourly_rate
        
        data_processing_cost = usage_metrics['total_gb_processed'] * data_processing_rate
        
        total_cost = hourly_cost + data_processing_cost
        monthly_cost = (total_cost / days) * 30  # Extrapolate to monthly
        
        return {
            'period_days': days,
            'hourly_cost': round(hourly_cost, 2),
            'data_processing_cost': round(data_processing_cost, 2),
            'total_period_cost': round(total_cost, 2),
            'estimated_monthly_cost': round(monthly_cost, 2),
            'cost_breakdown': {
                'hourly_percentage': round((hourly_cost / total_cost) * 100, 1) if total_cost > 0 else 0,
                'data_percentage': round((data_processing_cost / total_cost) * 100, 1) if total_cost > 0 else 0
            }
        }
    
    def _generate_nat_optimization_recommendations(self, nat_gw: Dict, 
                                                 usage_metrics: Dict, 
                                                 cost_analysis: Dict) -> List[Dict]:
        """Generate optimization recommendations for NAT Gateway"""
        recommendations = []
        
        if 'error' in usage_metrics or 'error' in cost_analysis:
            return recommendations
        
        daily_avg_gb = usage_metrics.get('daily_average_gb', 0)
        monthly_cost = cost_analysis.get('estimated_monthly_cost', 0)
        
        # Low usage recommendation
        if daily_avg_gb < 1:  # Less than 1GB per day
            recommendations.append({
                'type': 'consider_nat_instance',
                'priority': 'medium',
                'description': 'Low data usage - consider NAT instance for cost savings',
                'potential_savings': round(monthly_cost * 0.6, 2),  # ~60% savings
                'implementation_effort': 'medium'
            })
        
        # VPC Endpoints recommendation
        if daily_avg_gb > 5:  # Significant usage
            recommendations.append({
                'type': 'implement_vpc_endpoints',
                'priority': 'high',
                'description': 'High data usage - implement VPC endpoints for AWS services',
                'potential_savings': round(monthly_cost * 0.3, 2),  # ~30% savings
                'implementation_effort': 'low'
            })
        
        # Multi-AZ optimization
        recommendations.append({
            'type': 'review_multi_az_setup',
            'priority': 'low',
            'description': 'Review if multiple NAT Gateways are needed across AZs',
            'potential_savings': 'Variable',
            'implementation_effort': 'low'
        })
        
        return recommendations
```

### NAT Instance Alternative
```hcl
# NAT Instance as cost-effective alternative for low-traffic scenarios
resource "aws_instance" "nat_instance" {
  ami                    = data.aws_ami.nat_ami.id
  instance_type          = "t3.nano"  # Smallest instance for low traffic
  key_name              = var.key_name
  subnet_id             = aws_subnet.public.id
  vpc_security_group_ids = [aws_security_group.nat_instance.id]
  
  # Enable source/destination check disable
  source_dest_check = false
  
  # User data to configure NAT
  user_data = base64encode(templatefile("${path.module}/nat_instance_userdata.sh", {}))
  
  tags = {
    Name = "NAT Instance"
    Type = "NAT"
    CostOptimized = "true"
  }
}

# Security group for NAT instance
resource "aws_security_group" "nat_instance" {
  name_prefix = "nat-instance-"
  vpc_id      = aws_vpc.main.id
  
  # Allow HTTP/HTTPS from private subnets
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = [aws_subnet.private.cidr_block]
  }
  
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [aws_subnet.private.cidr_block]
  }
  
  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  tags = {
    Name = "NAT Instance Security Group"
  }
}

# Route table for private subnet to use NAT instance
resource "aws_route_table" "private_nat_instance" {
  vpc_id = aws_vpc.main.id
  
  route {
    cidr_block  = "0.0.0.0/0"
    instance_id = aws_instance.nat_instance.id
  }
  
  tags = {
    Name = "Private Route Table (NAT Instance)"
  }
}

# Data source for NAT AMI
data "aws_ami" "nat_ami" {
  most_recent = true
  owners      = ["amazon"]
  
  filter {
    name   = "name"
    values = ["amzn-ami-vpc-nat-*"]
  }
  
  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}
```

## 🔗 VPC Endpoints vs NAT Gateway

### VPC Endpoints Implementation
```hcl
# S3 VPC Endpoint (Gateway Endpoint - Free)
resource "aws_vpc_endpoint" "s3" {
  vpc_id       = aws_vpc.main.id
  service_name = "com.amazonaws.${var.region}.s3"
  
  route_table_ids = [
    aws_route_table.private.id
  ]
  
  tags = {
    Name = "S3 VPC Endpoint"
    CostOptimization = "true"
  }
}

# DynamoDB VPC Endpoint (Gateway Endpoint - Free)
resource "aws_vpc_endpoint" "dynamodb" {
  vpc_id       = aws_vpc.main.id
  service_name = "com.amazonaws.${var.region}.dynamodb"
  
  route_table_ids = [
    aws_route_table.private.id
  ]
  
  tags = {
    Name = "DynamoDB VPC Endpoint"
    CostOptimization = "true"
  }
}

# EC2 VPC Endpoint (Interface Endpoint - Paid)
resource "aws_vpc_endpoint" "ec2" {
  vpc_id              = aws_vpc.main.id
  service_name        = "com.amazonaws.${var.region}.ec2"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [aws_subnet.private.id]
  security_group_ids  = [aws_security_group.vpc_endpoint.id]
  
  private_dns_enabled = true
  
  tags = {
    Name = "EC2 VPC Endpoint"
    CostOptimization = "true"
  }
}

# Security group for interface endpoints
resource "aws_security_group" "vpc_endpoint" {
  name_prefix = "vpc-endpoint-"
  vpc_id      = aws_vpc.main.id
  
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.main.cidr_block]
  }
  
  tags = {
    Name = "VPC Endpoint Security Group"
  }
}

# CloudWatch Logs VPC Endpoint
resource "aws_vpc_endpoint" "logs" {
  vpc_id              = aws_vpc.main.id
  service_name        = "com.amazonaws.${var.region}.logs"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [aws_subnet.private.id]
  security_group_ids  = [aws_security_group.vpc_endpoint.id]
  
  private_dns_enabled = true
  
  tags = {
    Name = "CloudWatch Logs VPC Endpoint"
    CostOptimization = "true"
  }
}
```

### VPC Endpoint Cost Analysis
```python
class VPCEndpointAnalyzer:
    def __init__(self):
        self.ec2 = boto3.client('ec2')
        self.ce = boto3.client('ce')
        
    def analyze_vpc_endpoint_savings(self, vpc_id: str) -> Dict:
        """Analyze potential savings from VPC endpoints"""
        
        # Get current VPC endpoints
        endpoints = self.ec2.describe_vpc_endpoints(
            Filters=[{'Name': 'vpc-id', 'Values': [vpc_id]}]
        )
        
        # Analyze NAT Gateway usage for AWS service traffic
        nat_analysis = self._analyze_aws_service_traffic()
        
        # Calculate potential savings
        savings_analysis = self._calculate_endpoint_savings(nat_analysis)
        
        return {
            'vpc_id': vpc_id,
            'existing_endpoints': len(endpoints['VpcEndpoints']),
            'nat_gateway_analysis': nat_analysis,
            'savings_opportunities': savings_analysis,
            'recommendations': self._generate_endpoint_recommendations(savings_analysis)
        }
    
    def _analyze_aws_service_traffic(self) -> Dict:
        """Analyze traffic to AWS services that could use VPC endpoints"""
        # This would require VPC Flow Logs analysis
        # Simplified example
        
        estimated_aws_traffic = {
            's3': {'gb_per_month': 100, 'current_cost': 4.5},  # $0.045/GB
            'dynamodb': {'gb_per_month': 50, 'current_cost': 2.25},
            'ec2': {'gb_per_month': 20, 'current_cost': 0.9},
            'logs': {'gb_per_month': 10, 'current_cost': 0.45}
        }
        
        return estimated_aws_traffic
    
    def _calculate_endpoint_savings(self, traffic_analysis: Dict) -> Dict:
        """Calculate savings from implementing VPC endpoints"""
        savings = {}
        
        # Gateway endpoints (S3, DynamoDB) - Free
        gateway_services = ['s3', 'dynamodb']
        gateway_savings = 0
        
        for service in gateway_services:
            if service in traffic_analysis:
                gateway_savings += traffic_analysis[service]['current_cost']
        
        # Interface endpoints - Have hourly cost
        interface_hourly_cost = 0.01  # $0.01/hour per endpoint
        interface_data_cost = 0.01    # $0.01/GB
        
        interface_services = ['ec2', 'logs']
        interface_savings = 0
        interface_costs = 0
        
        for service in interface_services:
            if service in traffic_analysis:
                # Savings from avoiding NAT Gateway data processing
                nat_cost = traffic_analysis[service]['current_cost']
                
                # Cost of interface endpoint
                monthly_hours = 730
                endpoint_hourly_cost = interface_hourly_cost * monthly_hours
                endpoint_data_cost = traffic_analysis[service]['gb_per_month'] * interface_data_cost
                endpoint_total_cost = endpoint_hourly_cost + endpoint_data_cost
                
                net_savings = nat_cost - endpoint_total_cost
                
                if net_savings > 0:
                    interface_savings += net_savings
                else:
                    interface_costs += abs(net_savings)
        
        return {
            'gateway_endpoints': {
                'monthly_savings': round(gateway_savings, 2),
                'implementation_cost': 0,
                'net_savings': round(gateway_savings, 2)
            },
            'interface_endpoints': {
                'monthly_savings': round(interface_savings, 2),
                'monthly_costs': round(interface_costs, 2),
                'net_savings': round(interface_savings - interface_costs, 2)
            },
            'total_net_savings': round(gateway_savings + interface_savings - interface_costs, 2)
        }
    
    def _generate_endpoint_recommendations(self, savings_analysis: Dict) -> List[Dict]:
        """Generate VPC endpoint recommendations"""
        recommendations = []
        
        # Always recommend gateway endpoints (free)
        if savings_analysis['gateway_endpoints']['net_savings'] > 0:
            recommendations.append({
                'type': 'implement_gateway_endpoints',
                'services': ['S3', 'DynamoDB'],
                'priority': 'high',
                'monthly_savings': savings_analysis['gateway_endpoints']['net_savings'],
                'implementation_cost': 0,
                'effort': 'low'
            })
        
        # Recommend interface endpoints if savings are significant
        if savings_analysis['interface_endpoints']['net_savings'] > 10:  # $10+ savings
            recommendations.append({
                'type': 'implement_interface_endpoints',
                'services': ['EC2', 'CloudWatch Logs'],
                'priority': 'medium',
                'monthly_savings': savings_analysis['interface_endpoints']['net_savings'],
                'implementation_cost': savings_analysis['interface_endpoints']['monthly_costs'],
                'effort': 'medium'
            })
        
        return recommendations
```

## 🌍 CloudFront and CDN Optimization

### CloudFront Cost Optimization
```hcl
# Cost-optimized CloudFront distribution
resource "aws_cloudfront_distribution" "cost_optimized" {
  origin {
    domain_name = aws_s3_bucket.content.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.content.bucket}"
    
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.oai.cloudfront_access_identity_path
    }
  }
  
  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"
  
  # Cost optimization: Use price class with fewer edge locations
  price_class = "PriceClass_100"  # US, Canada, Europe only
  
  default_cache_behavior {
    allowed_methods        = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "S3-${aws_s3_bucket.content.bucket}"
    compress               = true  # Enable compression to reduce data transfer
    
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    
    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 86400   # 1 day
    max_ttl                = 31536000 # 1 year
  }
  
  # Cache behavior for static assets (longer TTL)
  ordered_cache_behavior {
    path_pattern     = "/static/*"
    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${aws_s3_bucket.content.bucket}"
    compress         = true
    
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    
    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 31536000  # 1 year
    default_ttl            = 31536000  # 1 year
    max_ttl                = 31536000  # 1 year
  }
  
  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
  
  viewer_certificate {
    cloudfront_default_certificate = true
  }
  
  tags = {
    Name = "Cost Optimized Distribution"
    CostOptimization = "true"
  }
}
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Do you understand NAT Gateway cost components?
- [ ] Can you implement VPC endpoints effectively?
- [ ] Do you know how to optimize CloudFront costs?
- [ ] Can you analyze network traffic patterns?

### Practical Skills
- [ ] Can you analyze NAT Gateway usage and costs?
- [ ] Do you know when to use NAT instances vs NAT gateways?
- [ ] Can you implement cost-effective VPC endpoint strategies?
- [ ] Do you understand CDN optimization techniques?

## 📋 Practical Examples

See the `examples/` directory for:
- NAT Gateway cost analysis scripts
- VPC endpoint implementation templates
- CloudFront optimization configurations
- Network traffic monitoring tools

## ➡️ Next Steps
Continue to [Module 12: Serverless Cost Optimization](../12_serverless_cost_optimization/)

---
**Module Duration**: 1 week  
**Difficulty**: Advanced  
**Prerequisites**: Module 10 completion
