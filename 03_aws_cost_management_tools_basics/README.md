# 📘 Module 03: AWS Cost Management Tools Basics

## 🎯 Learning Goals
By the end of this module, you will:
- Master AWS native cost management tools
- Set up comprehensive cost monitoring and alerting
- Create custom cost dashboards and reports
- Integrate third-party tools for enhanced cost visibility
- Implement automated cost anomaly detection

## 📚 Table of Contents
1. [AWS Cost Explorer](#aws-cost-explorer)
2. [AWS Budgets and Alerts](#aws-budgets-and-alerts)
3. [AWS Cost Anomaly Detection](#aws-cost-anomaly-detection)
4. [Cost and Usage Report (CUR)](#cost-and-usage-report-cur)
5. [Free Tools Integration](#free-tools-integration)
6. [Premium Tools Overview](#premium-tools-overview)
7. [Study Checkpoints](#study-checkpoints)
8. [Practical Examples](#practical-examples)

## 🔍 AWS Cost Explorer

### Overview
AWS Cost Explorer is a native tool for visualizing, understanding, and managing AWS costs and usage over time.

### Key Features
- **Interactive dashboards** with customizable views
- **Forecasting** based on historical data
- **Filtering and grouping** by multiple dimensions
- **Savings recommendations** for Reserved Instances and Savings Plans
- **API access** for programmatic cost analysis

### Core Capabilities
```yaml
cost_explorer_features:
  visualization:
    - Cost and usage charts
    - Trend analysis
    - Comparative views
    - Custom date ranges
    
  grouping_dimensions:
    - Service
    - Account
    - Region
    - Availability Zone
    - Instance Type
    - Usage Type
    - Tags
    
  filtering_options:
    - Time period
    - Services
    - Accounts
    - Regions
    - Tags
    - Charge types
```

### Practical Implementation
```python
# Cost Explorer API Example
import boto3
from datetime import datetime, timedelta

def get_monthly_costs_by_service():
    client = boto3.client('ce')
    
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    response = client.get_cost_and_usage(
        TimePeriod={
            'Start': start_date,
            'End': end_date
        },
        Granularity='MONTHLY',
        Metrics=['BlendedCost'],
        GroupBy=[
            {
                'Type': 'DIMENSION',
                'Key': 'SERVICE'
            }
        ]
    )
    
    return response['ResultsByTime']
```

### Best Practices
- **Regular monitoring**: Set up weekly cost reviews
- **Custom reports**: Create reports for different stakeholders
- **Forecasting**: Use 12-month forecasts for budget planning
- **Savings opportunities**: Review RI/SP recommendations monthly

## 💰 AWS Budgets and Alerts

### Budget Types
1. **Cost Budgets**: Track spending against budget thresholds
2. **Usage Budgets**: Monitor service usage quantities
3. **Reservation Budgets**: Track RI/SP utilization and coverage
4. **Savings Plans Budgets**: Monitor Savings Plans performance

### Alert Configuration
```yaml
budget_alert_configuration:
  cost_budget:
    name: "Monthly-Production-Budget"
    amount: 10000  # USD
    time_unit: "MONTHLY"
    
    alerts:
      - threshold: 80
        type: "ACTUAL"
        notification: "email"
        recipients: ["<EMAIL>"]
        
      - threshold: 100
        type: "FORECASTED"
        notification: "slack"
        webhook: "https://hooks.slack.com/..."
        
      - threshold: 120
        type: "ACTUAL"
        notification: "sns"
        topic_arn: "arn:aws:sns:us-east-1:123456789012:cost-alerts"

  usage_budget:
    name: "EC2-Instance-Hours"
    amount: 1000  # hours
    time_unit: "MONTHLY"
    service: "Amazon Elastic Compute Cloud - Compute"
    
    alerts:
      - threshold: 90
        type: "ACTUAL"
        notification: "email"
```

### Implementation with Terraform
```hcl
# Terraform Budget Configuration
resource "aws_budgets_budget" "monthly_cost_budget" {
  name         = "monthly-cost-budget"
  budget_type  = "COST"
  limit_amount = "10000"
  limit_unit   = "USD"
  time_unit    = "MONTHLY"
  
  cost_filters = {
    Tag = {
      Environment = ["prod"]
    }
  }
  
  notification {
    comparison_operator        = "GREATER_THAN"
    threshold                 = 80
    threshold_type            = "PERCENTAGE"
    notification_type         = "ACTUAL"
    subscriber_email_addresses = ["<EMAIL>"]
  }
  
  notification {
    comparison_operator        = "GREATER_THAN"
    threshold                 = 100
    threshold_type            = "PERCENTAGE"
    notification_type          = "FORECASTED"
    subscriber_email_addresses = ["<EMAIL>"]
  }
}
```

## 🚨 AWS Cost Anomaly Detection

### How It Works
AWS Cost Anomaly Detection uses machine learning to identify unusual spending patterns and automatically sends alerts.

### Key Features
- **Machine learning-based** anomaly detection
- **Automatic baseline** establishment
- **Customizable sensitivity** settings
- **Multiple notification** channels
- **Root cause analysis** suggestions

### Configuration Example
```yaml
anomaly_detection_setup:
  cost_monitor:
    name: "Production-Environment-Monitor"
    monitor_type: "DIMENSIONAL"
    
    specification:
      dimension: "TAG"
      key: "Environment"
      values: ["prod"]
      
    threshold_expression:
      and:
        - dimension: "ANOMALY_TOTAL_IMPACT_ABSOLUTE"
          values: ["100"]  # Alert if anomaly > $100
          
  subscription:
    name: "FinOps-Team-Alerts"
    frequency: "IMMEDIATE"
    threshold: 0.1  # 10% threshold
    
    subscribers:
      - address: "<EMAIL>"
        type: "EMAIL"
      - address: "arn:aws:sns:us-east-1:123456789012:cost-anomalies"
        type: "SNS"
```

### Terraform Implementation
```hcl
resource "aws_ce_anomaly_detector" "production_anomaly_detector" {
  name         = "production-cost-anomaly-detector"
  monitor_type = "DIMENSIONAL"
  
  specification = jsonencode({
    Dimension = "TAG"
    Key       = "Environment"
    Values    = ["prod"]
  })
}

resource "aws_ce_anomaly_subscription" "finops_alerts" {
  name      = "finops-anomaly-alerts"
  frequency = "IMMEDIATE"
  
  monitor_arn_list = [
    aws_ce_anomaly_detector.production_anomaly_detector.arn
  ]
  
  subscriber {
    type    = "EMAIL"
    address = "<EMAIL>"
  }
  
  threshold_expression {
    and {
      dimension {
        key           = "ANOMALY_TOTAL_IMPACT_ABSOLUTE"
        values        = ["100"]
        match_options = ["GREATER_THAN_OR_EQUAL"]
      }
    }
  }
}
```

## 📊 Cost and Usage Report (CUR)

### Overview
The most detailed billing data available from AWS, delivered to S3 for analysis.

### Key Features
- **Hourly granularity** for detailed analysis
- **Resource-level details** with tags
- **Pricing information** and discounts
- **Usage quantities** and rates
- **Integration ready** for analytics tools

### Setup Configuration
```yaml
cur_configuration:
  report_name: "detailed-billing-report"
  time_unit: "HOURLY"
  format: "Parquet"  # More efficient than CSV
  compression: "GZIP"
  
  additional_schema_elements:
    - "RESOURCES"
    - "SPLIT_COST_ALLOCATION_DATA"
    
  s3_configuration:
    bucket: "company-billing-reports"
    prefix: "cur-reports/"
    region: "us-east-1"
    
  refresh_closed_reports: true
  report_versioning: "OVERWRITE_REPORT"
```

### Athena Integration
```sql
-- Create Athena table for CUR analysis
CREATE EXTERNAL TABLE cur_table (
  identity_line_item_id string,
  identity_time_interval string,
  bill_invoice_id string,
  bill_billing_entity string,
  bill_bill_type string,
  bill_payer_account_id string,
  bill_billing_period_start_date string,
  bill_billing_period_end_date string,
  line_item_usage_account_id string,
  line_item_line_item_type string,
  line_item_usage_start_date string,
  line_item_usage_end_date string,
  line_item_product_code string,
  line_item_usage_type string,
  line_item_operation string,
  line_item_availability_zone string,
  line_item_resource_id string,
  line_item_usage_amount double,
  line_item_normalization_factor double,
  line_item_normalized_usage_amount double,
  line_item_currency_code string,
  line_item_unblended_rate string,
  line_item_unblended_cost double,
  line_item_blended_rate string,
  line_item_blended_cost double,
  line_item_line_item_description string,
  line_item_tax_type string,
  product_product_name string,
  pricing_rate_code string,
  pricing_rate_id string,
  pricing_currency string,
  pricing_public_on_demand_cost double,
  pricing_public_on_demand_rate string,
  pricing_term string,
  pricing_unit string,
  reservation_amortized_upfront_cost_for_usage double,
  reservation_amortized_upfront_fee_for_billing_period double,
  reservation_effective_cost double,
  reservation_end_time string,
  reservation_modification_status string,
  reservation_normalized_units_per_reservation string,
  reservation_number_of_reservations string,
  reservation_recurring_fee_for_usage double,
  reservation_start_time string,
  reservation_subscription_id string,
  reservation_total_reserved_normalized_units string,
  reservation_total_reserved_units string,
  reservation_units_per_reservation string,
  reservation_unused_amortized_upfront_fee_for_billing_period double,
  reservation_unused_normalized_unit_quantity double,
  reservation_unused_quantity double,
  reservation_unused_recurring_fee double,
  reservation_upfront_value double,
  savings_plan_total_commitment_to_date double,
  savings_plan_savings_plan_arn string,
  savings_plan_savings_plan_rate double,
  savings_plan_used_commitment double,
  savings_plan_savings_plan_effective_cost double,
  savings_plan_amortized_upfront_commitment_for_billing_period double,
  savings_plan_recurring_commitment_for_billing_period double
)
PARTITIONED BY (
  year string,
  month string
)
STORED AS PARQUET
LOCATION 's3://company-billing-reports/cur-reports/'
```

## 🆓 Free Tools Integration

### AWS CLI Cost Analysis
```bash
#!/bin/bash
# Daily cost analysis script

# Get yesterday's costs by service
aws ce get-cost-and-usage \
  --time-period Start=$(date -d "yesterday" +%Y-%m-%d),End=$(date +%Y-%m-%d) \
  --granularity DAILY \
  --metrics BlendedCost \
  --group-by Type=DIMENSION,Key=SERVICE \
  --output table

# Get top 10 most expensive resources
aws ce get-dimension-values \
  --dimension RESOURCE_ID \
  --time-period Start=$(date -d "30 days ago" +%Y-%m-%d),End=$(date +%Y-%m-%d) \
  --search-string "" \
  --max-results 10
```

### Athena Cost Queries
```sql
-- Top 10 most expensive services this month
SELECT 
  line_item_product_code,
  SUM(line_item_unblended_cost) as total_cost
FROM cur_table
WHERE 
  year = '2024' 
  AND month = '01'
  AND line_item_line_item_type = 'Usage'
GROUP BY line_item_product_code
ORDER BY total_cost DESC
LIMIT 10;

-- Daily cost trend for last 30 days
SELECT 
  line_item_usage_start_date,
  SUM(line_item_unblended_cost) as daily_cost
FROM cur_table
WHERE 
  line_item_usage_start_date >= date_add('day', -30, current_date)
  AND line_item_line_item_type = 'Usage'
GROUP BY line_item_usage_start_date
ORDER BY line_item_usage_start_date;
```

## 💎 Premium Tools Overview

### CloudZero
- **Real-time cost allocation** with automatic tagging
- **Unit cost metrics** (cost per customer, feature, etc.)
- **Anomaly detection** with ML-powered insights
- **Engineering-focused** dashboards and alerts

### Apptio Cloudability
- **Multi-cloud cost management** (AWS, Azure, GCP)
- **Advanced analytics** and forecasting
- **Optimization recommendations** with ROI analysis
- **Enterprise-grade** governance and reporting

### Key Differentiators
```yaml
premium_tools_comparison:
  cloudability:
    strengths:
      - Multi-cloud support
      - Enterprise features
      - Advanced analytics
      - Strong governance
    pricing: "Enterprise (contact sales)"
    
  cloudzero:
    strengths:
      - Engineering focus
      - Real-time allocation
      - Unit economics
      - Developer-friendly
    pricing: "Usage-based"
    
  selection_criteria:
    - Organization size
    - Multi-cloud requirements
    - Engineering vs finance focus
    - Budget constraints
    - Integration requirements
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Can you navigate AWS Cost Explorer effectively?
- [ ] Do you understand how to set up budgets and alerts?
- [ ] Can you configure Cost Anomaly Detection?
- [ ] Do you know how to set up and use CUR?

### Practical Skills
- [ ] Can you create custom cost reports?
- [ ] Do you know how to query CUR data with Athena?
- [ ] Can you integrate cost data with external tools?
- [ ] Do you understand when to use premium tools?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete AWS CLI cost analysis scripts
- Terraform configurations for all tools
- Athena query library for cost analysis
- Dashboard templates and configurations

## ➡️ Next Steps
Continue to [Module 04: Tagging and Resource Ownership](../04_tagging_and_resource_ownership/)

---
**Module Duration**: 1 week  
**Difficulty**: Beginner  
**Prerequisites**: Module 02 completion
