# 📘 Module 08: Reserved Instances and Savings Plans

## 🎯 Learning Goals
By the end of this module, you will:
- Understand the differences between Reserved Instances and Savings Plans
- Develop optimal purchase strategies and break-even calculations
- Implement automated RI/SP recommendation analysis
- Set up monitoring for utilization and coverage
- Leverage both free and premium tools for commitment optimization

## 📚 Table of Contents
1. [RI vs Savings Plans Comparison](#ri-vs-savings-plans-comparison)
2. [Purchase Strategy Development](#purchase-strategy-development)
3. [Break-even Analysis and ROI](#break-even-analysis-and-roi)
4. [Automated Recommendation Analysis](#automated-recommendation-analysis)
5. [Utilization Monitoring](#utilization-monitoring)
6. [Tools and Integration](#tools-and-integration)
7. [Study Checkpoints](#study-checkpoints)
8. [Practical Examples](#practical-examples)

## 🔄 RI vs Savings Plans Comparison

### Reserved Instances (RIs)
```yaml
reserved_instances:
  characteristics:
    scope: "Specific instance family and region"
    flexibility: "Limited to instance family"
    discount: "Up to 75% vs On-Demand"
    commitment: "1 or 3 years"
    
  types:
    standard_ri:
      discount: "Up to 75%"
      flexibility: "No instance type changes"
      convertible: false
      
    convertible_ri:
      discount: "Up to 54%"
      flexibility: "Can change instance family, OS, tenancy"
      convertible: true
      
  payment_options:
    no_upfront: "0% upfront, monthly payments"
    partial_upfront: "~50% upfront, reduced monthly"
    all_upfront: "100% upfront, highest discount"
    
  best_for:
    - "Predictable, steady-state workloads"
    - "Specific instance type requirements"
    - "Maximum discount priority"
```

### Savings Plans
```yaml
savings_plans:
  characteristics:
    scope: "Compute usage across services"
    flexibility: "High - any instance type, region, OS"
    discount: "Up to 72% vs On-Demand"
    commitment: "1 or 3 years"
    
  types:
    compute_savings_plans:
      scope: "EC2, Fargate, Lambda"
      flexibility: "Maximum flexibility"
      discount: "Up to 66%"
      
    ec2_instance_savings_plans:
      scope: "EC2 in specific region"
      flexibility: "Instance family flexibility"
      discount: "Up to 72%"
      
  payment_options:
    no_upfront: "0% upfront, monthly payments"
    partial_upfront: "~50% upfront, reduced monthly"
    all_upfront: "100% upfront, highest discount"
    
  best_for:
    - "Dynamic, changing workloads"
    - "Multi-service compute usage"
    - "Flexibility priority"
```

### Decision Matrix
```yaml
decision_framework:
  choose_reserved_instances_when:
    - "Workload is predictable and stable"
    - "Specific instance types are required"
    - "Maximum discount is priority"
    - "Single region deployment"
    
  choose_savings_plans_when:
    - "Workload patterns change frequently"
    - "Multi-region deployments"
    - "Using multiple compute services"
    - "Flexibility is more important than maximum discount"
    
  hybrid_approach:
    - "Use Savings Plans for base capacity"
    - "Use RIs for specific, stable workloads"
    - "Combine with Spot for variable capacity"
```

## 📊 Purchase Strategy Development

### Capacity Planning and Analysis
```python
import boto3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List

class CommitmentAnalyzer:
    def __init__(self):
        self.ce = boto3.client('ce')
        self.ec2 = boto3.client('ec2')
        
    def analyze_historical_usage(self, months: int = 12) -> Dict:
        """Analyze historical EC2 usage patterns"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=months*30)).strftime('%Y-%m-%d')
        
        # Get usage data
        response = self.ce.get_cost_and_usage(
            TimePeriod={'Start': start_date, 'End': end_date},
            Granularity='MONTHLY',
            Metrics=['UsageQuantity'],
            GroupBy=[
                {'Type': 'DIMENSION', 'Key': 'INSTANCE_TYPE'},
                {'Type': 'DIMENSION', 'Key': 'REGION'}
            ],
            Filter={
                'Dimensions': {
                    'Key': 'SERVICE',
                    'Values': ['Amazon Elastic Compute Cloud - Compute']
                }
            }
        )
        
        usage_data = []
        for result in response['ResultsByTime']:
            period = result['TimePeriod']['Start']
            for group in result['Groups']:
                instance_type = group['Keys'][0]
                region = group['Keys'][1]
                usage = float(group['Metrics']['UsageQuantity']['Amount'])
                
                usage_data.append({
                    'period': period,
                    'instance_type': instance_type,
                    'region': region,
                    'usage_hours': usage
                })
        
        df = pd.DataFrame(usage_data)
        
        # Calculate statistics
        stats = df.groupby(['instance_type', 'region']).agg({
            'usage_hours': ['mean', 'min', 'max', 'std']
        }).round(2)
        
        return {
            'raw_data': usage_data,
            'statistics': stats.to_dict(),
            'recommendations': self._generate_commitment_recommendations(df)
        }
    
    def _generate_commitment_recommendations(self, df: pd.DataFrame) -> List[Dict]:
        """Generate commitment recommendations based on usage patterns"""
        recommendations = []
        
        # Group by instance type and region
        grouped = df.groupby(['instance_type', 'region'])
        
        for (instance_type, region), group in grouped:
            monthly_usage = group['usage_hours'].values
            
            # Calculate baseline usage (minimum consistent usage)
            baseline_usage = min(monthly_usage)
            
            # Calculate variability
            avg_usage = monthly_usage.mean()
            std_usage = monthly_usage.std()
            variability = std_usage / avg_usage if avg_usage > 0 else 0
            
            # Recommendation logic
            if baseline_usage > 100:  # At least 100 hours/month
                if variability < 0.2:  # Low variability
                    commitment_type = "Reserved Instance"
                    commitment_hours = baseline_usage * 0.8  # Conservative
                elif variability < 0.5:  # Medium variability
                    commitment_type = "Savings Plan"
                    commitment_hours = baseline_usage * 0.6
                else:  # High variability
                    commitment_type = "Spot + On-Demand"
                    commitment_hours = 0
                
                if commitment_hours > 0:
                    recommendations.append({
                        'instance_type': instance_type,
                        'region': region,
                        'commitment_type': commitment_type,
                        'recommended_hours': round(commitment_hours, 0),
                        'baseline_usage': round(baseline_usage, 0),
                        'average_usage': round(avg_usage, 0),
                        'variability': round(variability, 2)
                    })
        
        return recommendations
    
    def get_current_recommendations(self) -> Dict:
        """Get current AWS recommendations for RIs and Savings Plans"""
        # Get RI recommendations
        ri_recommendations = self.ce.get_rightsizing_recommendation(
            Service='AmazonEC2'
        )
        
        # Get Savings Plans recommendations
        sp_recommendations = self.ce.get_savings_plans_purchase_recommendation(
            SavingsPlansType='COMPUTE_SP',
            TermInYears='ONE_YEAR',
            PaymentOption='NO_UPFRONT'
        )
        
        return {
            'reserved_instances': ri_recommendations,
            'savings_plans': sp_recommendations
        }
```

### Break-even Analysis Implementation
```python
class BreakEvenCalculator:
    def __init__(self):
        self.pricing_data = {
            # Simplified pricing data (varies by region)
            'm5.large': {'on_demand': 0.096, 'ri_1yr_no_upfront': 0.062},
            'm5.xlarge': {'on_demand': 0.192, 'ri_1yr_no_upfront': 0.124},
            'c5.large': {'on_demand': 0.085, 'ri_1yr_no_upfront': 0.055},
        }
    
    def calculate_ri_breakeven(self, instance_type: str, hours_per_month: int,
                              term_years: int = 1, payment_option: str = 'no_upfront') -> Dict:
        """Calculate break-even analysis for Reserved Instance"""
        
        if instance_type not in self.pricing_data:
            return {'error': f'Pricing data not available for {instance_type}'}
        
        on_demand_hourly = self.pricing_data[instance_type]['on_demand']
        ri_hourly = self.pricing_data[instance_type]['ri_1yr_no_upfront']
        
        # Calculate costs
        monthly_on_demand_cost = on_demand_hourly * hours_per_month
        monthly_ri_cost = ri_hourly * hours_per_month
        monthly_savings = monthly_on_demand_cost - monthly_ri_cost
        
        # Calculate break-even
        total_commitment_months = term_years * 12
        total_savings = monthly_savings * total_commitment_months
        savings_percentage = (monthly_savings / monthly_on_demand_cost) * 100
        
        # Break-even hours (minimum hours needed to justify RI)
        breakeven_hours_monthly = (ri_hourly * 730) / on_demand_hourly
        
        return {
            'instance_type': instance_type,
            'analysis_period': f'{term_years} year(s)',
            'hours_per_month': hours_per_month,
            'on_demand_hourly_rate': on_demand_hourly,
            'ri_hourly_rate': ri_hourly,
            'monthly_on_demand_cost': round(monthly_on_demand_cost, 2),
            'monthly_ri_cost': round(monthly_ri_cost, 2),
            'monthly_savings': round(monthly_savings, 2),
            'annual_savings': round(monthly_savings * 12, 2),
            'total_savings': round(total_savings, 2),
            'savings_percentage': round(savings_percentage, 1),
            'breakeven_hours_monthly': round(breakeven_hours_monthly, 0),
            'recommendation': 'Purchase RI' if hours_per_month >= breakeven_hours_monthly else 'Use On-Demand'
        }
    
    def calculate_savings_plan_breakeven(self, hourly_commitment: float,
                                       actual_usage_hours: int, term_years: int = 1) -> Dict:
        """Calculate break-even for Savings Plans"""
        
        # Simplified Savings Plan discount (varies by commitment and term)
        sp_discount = 0.20  # 20% discount example
        
        monthly_commitment_cost = hourly_commitment * 730  # Full month commitment
        monthly_actual_usage_cost = hourly_commitment * actual_usage_hours * (1 - sp_discount)
        
        # If usage exceeds commitment, pay On-Demand for excess
        if actual_usage_hours > hourly_commitment * 730:
            excess_hours = actual_usage_hours - (hourly_commitment * 730)
            # Assuming average On-Demand rate
            excess_cost = excess_hours * 0.10  # $0.10/hour average
            monthly_actual_usage_cost += excess_cost
        
        monthly_on_demand_cost = actual_usage_hours * 0.10  # Average On-Demand
        monthly_savings = monthly_on_demand_cost - monthly_actual_usage_cost
        
        return {
            'hourly_commitment': hourly_commitment,
            'actual_usage_hours': actual_usage_hours,
            'monthly_commitment_cost': round(monthly_commitment_cost, 2),
            'monthly_actual_cost': round(monthly_actual_usage_cost, 2),
            'monthly_on_demand_cost': round(monthly_on_demand_cost, 2),
            'monthly_savings': round(monthly_savings, 2),
            'annual_savings': round(monthly_savings * 12, 2),
            'utilization_percentage': round((actual_usage_hours / (hourly_commitment * 730)) * 100, 1),
            'recommendation': 'Good commitment' if monthly_savings > 0 else 'Reduce commitment'
        }
```

## 📈 Utilization Monitoring

### RI/SP Utilization Tracking
```python
class UtilizationMonitor:
    def __init__(self):
        self.ce = boto3.client('ce')
        self.cloudwatch = boto3.client('cloudwatch')
        
    def get_ri_utilization(self, time_period_days: int = 30) -> Dict:
        """Get Reserved Instance utilization metrics"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=time_period_days)).strftime('%Y-%m-%d')
        
        response = self.ce.get_reservation_utilization(
            TimePeriod={'Start': start_date, 'End': end_date},
            Granularity='MONTHLY',
            GroupBy=[
                {'Type': 'DIMENSION', 'Key': 'INSTANCE_TYPE'},
                {'Type': 'DIMENSION', 'Key': 'REGION'}
            ]
        )
        
        utilization_data = []
        for result in response['UtilizationsByTime']:
            period = result['TimePeriod']['Start']
            
            for group in result['Groups']:
                instance_type = group['Keys'][0] if group['Keys'] else 'Unknown'
                region = group['Keys'][1] if len(group['Keys']) > 1 else 'Unknown'
                
                utilization = group['Utilization']
                
                utilization_data.append({
                    'period': period,
                    'instance_type': instance_type,
                    'region': region,
                    'utilization_percentage': float(utilization['UtilizationPercentage']),
                    'purchased_hours': float(utilization['PurchasedHours']),
                    'used_hours': float(utilization['UsedHours']),
                    'unused_hours': float(utilization['UnusedHours'])
                })
        
        return utilization_data
    
    def get_savings_plans_utilization(self, time_period_days: int = 30) -> Dict:
        """Get Savings Plans utilization metrics"""
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=time_period_days)).strftime('%Y-%m-%d')
        
        response = self.ce.get_savings_plans_utilization(
            TimePeriod={'Start': start_date, 'End': end_date},
            Granularity='MONTHLY'
        )
        
        utilization_data = []
        for result in response['SavingsPlansUtilizationsByTime']:
            period = result['TimePeriod']['Start']
            utilization = result['Utilization']
            
            utilization_data.append({
                'period': period,
                'utilization_percentage': float(utilization['UtilizationPercentage']),
                'used_commitment': float(utilization['UsedCommitment']),
                'unused_commitment': float(utilization['UnusedCommitment']),
                'total_commitment': float(utilization['TotalCommitment'])
            })
        
        return utilization_data
    
    def create_utilization_alerts(self, threshold: float = 80.0) -> None:
        """Create CloudWatch alarms for low utilization"""
        
        # Create custom metric for RI utilization
        self.cloudwatch.put_metric_alarm(
            AlarmName='RI-Low-Utilization',
            ComparisonOperator='LessThanThreshold',
            EvaluationPeriods=2,
            MetricName='ReservedInstanceUtilization',
            Namespace='AWS/Billing',
            Period=86400,  # Daily
            Statistic='Average',
            Threshold=threshold,
            ActionsEnabled=True,
            AlarmActions=[
                'arn:aws:sns:us-east-1:123456789012:ri-alerts'
            ],
            AlarmDescription='Alert when RI utilization is low',
            Unit='Percent'
        )
        
        # Create custom metric for Savings Plans utilization
        self.cloudwatch.put_metric_alarm(
            AlarmName='SavingsPlans-Low-Utilization',
            ComparisonOperator='LessThanThreshold',
            EvaluationPeriods=2,
            MetricName='SavingsPlansUtilization',
            Namespace='AWS/Billing',
            Period=86400,  # Daily
            Statistic='Average',
            Threshold=threshold,
            ActionsEnabled=True,
            AlarmActions=[
                'arn:aws:sns:us-east-1:123456789012:sp-alerts'
            ],
            AlarmDescription='Alert when Savings Plans utilization is low',
            Unit='Percent'
        )
```

## 🛠️ Tools and Integration

### Free Tools

#### AWS Cost Explorer RI/SP Recommendations
```yaml
aws_native_tools:
  cost_explorer:
    features:
      - "RI purchase recommendations"
      - "Savings Plans recommendations"
      - "Utilization and coverage reports"
      - "Historical analysis"
    
    limitations:
      - "Basic recommendation logic"
      - "Limited customization"
      - "Manual analysis required"
      
  aws_cli_integration:
    commands:
      - "aws ce get-rightsizing-recommendation"
      - "aws ce get-savings-plans-purchase-recommendation"
      - "aws ce get-reservation-utilization"
      - "aws ce get-savings-plans-utilization"
```

#### Infracost Integration
```yaml
# .infracost/config.yml
version: "0.1"

projects:
  - path: terraform/
    terraform_plan_flags: "-var-file=prod.tfvars"
    
pricing:
  currency: USD
  
savings_plans:
  enabled: true
  commitment_percentage: 70  # 70% of usage covered by SP
  
reserved_instances:
  enabled: true
  term: "1yr"
  payment_option: "no_upfront"
```

### Premium Tools

#### Apptio Cloudability
```yaml
cloudability_features:
  ri_sp_optimization:
    - "Advanced recommendation engine"
    - "Portfolio optimization"
    - "Risk analysis and modeling"
    - "Automated purchase workflows"
    
  analytics:
    - "Multi-dimensional analysis"
    - "What-if scenarios"
    - "ROI tracking"
    - "Commitment lifecycle management"
    
  governance:
    - "Approval workflows"
    - "Budget integration"
    - "Policy enforcement"
    - "Audit trails"
```

#### CloudCheckr
```yaml
cloudcheckr_features:
  commitment_optimization:
    - "ML-powered recommendations"
    - "Portfolio-level optimization"
    - "Risk assessment"
    - "Automated monitoring"
    
  reporting:
    - "Executive dashboards"
    - "Detailed utilization reports"
    - "Trend analysis"
    - "Custom reporting"
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Do you understand the differences between RIs and Savings Plans?
- [ ] Can you perform break-even analysis for commitments?
- [ ] Do you know how to monitor utilization effectively?
- [ ] Can you develop optimal purchase strategies?

### Practical Skills
- [ ] Can you analyze historical usage patterns?
- [ ] Do you know how to calculate ROI for commitments?
- [ ] Can you set up automated monitoring and alerts?
- [ ] Do you understand when to use premium tools?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete commitment analysis scripts
- Break-even calculation tools
- Utilization monitoring setups
- Automated recommendation workflows

## ➡️ Next Steps
Continue to [Module 09: Storage and S3 Cost Controls](../09_storage_and_s3_cost_controls/)

---
**Module Duration**: 1 week  
**Difficulty**: Intermediate  
**Prerequisites**: Module 07 completion
