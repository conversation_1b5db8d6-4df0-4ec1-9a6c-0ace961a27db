# 📘 Module 07: Rightsizing and Autoscaling

## 🎯 Learning Goals
By the end of this module, you will:
- Master AWS Compute Optimizer for rightsizing recommendations
- Implement intelligent autoscaling strategies with guardrails
- Optimize Lambda functions for cost and performance
- Set up Kubernetes resource limits and requests properly
- Leverage both free and premium tools for rightsizing

## 📚 Table of Contents
1. [AWS Compute Optimizer Deep Dive](#aws-compute-optimizer-deep-dive)
2. [EC2 Rightsizing Strategies](#ec2-rightsizing-strategies)
3. [Lambda Function Optimization](#lambda-function-optimization)
4. [Kubernetes Resource Management](#kubernetes-resource-management)
5. [Auto Scaling with Guardrails](#auto-scaling-with-guardrails)
6. [Tools and Integration](#tools-and-integration)
7. [Study Checkpoints](#study-checkpoints)
8. [Practical Examples](#practical-examples)

## 🔍 AWS Compute Optimizer Deep Dive

### Understanding Compute Optimizer
```yaml
compute_optimizer_overview:
  purpose: "ML-powered rightsizing recommendations"
  supported_resources:
    - EC2 instances
    - Auto Scaling groups
    - EBS volumes
    - Lambda functions
    - ECS services (Fargate)
    
  recommendation_types:
    - Under-provisioned
    - Over-provisioned
    - Optimized
    - None available
    
  metrics_analyzed:
    ec2:
      - CPU utilization
      - Memory utilization (with CloudWatch agent)
      - Network utilization
      - EBS IOPS
    lambda:
      - Duration
      - Memory utilization
      - Invocation patterns
```

### Implementing Compute Optimizer Recommendations
```python
import boto3
import json
from datetime import datetime, timedelta
from typing import List, Dict

class ComputeOptimizerAnalyzer:
    def __init__(self):
        self.compute_optimizer = boto3.client('compute-optimizer')
        self.ec2 = boto3.client('ec2')
        self.pricing = boto3.client('pricing', region_name='us-east-1')
        
    def get_ec2_recommendations(self, account_ids: List[str] = None) -> List[Dict]:
        """Get EC2 rightsizing recommendations"""
        recommendations = []
        
        paginator = self.compute_optimizer.get_paginator('get_ec2_instance_recommendations')
        
        page_iterator = paginator.paginate(
            accountIds=account_ids or [],
            filters=[
                {
                    'name': 'Finding',
                    'values': ['Underprovisioned', 'Overprovisioned']
                }
            ]
        )
        
        for page in page_iterator:
            for recommendation in page['instanceRecommendations']:
                instance_id = recommendation['instanceArn'].split('/')[-1]
                current_instance = recommendation['currentInstanceType']
                
                # Get best recommendation option
                best_option = None
                max_savings = 0
                
                for option in recommendation['recommendationOptions']:
                    estimated_savings = option.get('estimatedMonthlySavings', {})
                    savings_value = float(estimated_savings.get('value', 0))
                    
                    if savings_value > max_savings:
                        max_savings = savings_value
                        best_option = option
                
                if best_option:
                    recommendations.append({
                        'instance_id': instance_id,
                        'current_type': current_instance,
                        'recommended_type': best_option['instanceType'],
                        'finding': recommendation['finding'],
                        'estimated_monthly_savings': max_savings,
                        'savings_currency': estimated_savings.get('currency', 'USD'),
                        'performance_risk': best_option.get('performanceRisk', 'Unknown'),
                        'utilization_metrics': recommendation.get('utilizationMetrics', {}),
                        'tags': recommendation.get('tags', [])
                    })
        
        return recommendations
    
    def get_lambda_recommendations(self) -> List[Dict]:
        """Get Lambda function rightsizing recommendations"""
        recommendations = []
        
        paginator = self.compute_optimizer.get_paginator('get_lambda_function_recommendations')
        
        for page in paginator.paginate():
            for recommendation in page['lambdaFunctionRecommendations']:
                function_arn = recommendation['functionArn']
                current_memory = recommendation['currentMemorySize']
                
                best_option = None
                max_savings = 0
                
                for option in recommendation['memorySizeRecommendationOptions']:
                    estimated_savings = option.get('estimatedMonthlySavings', {})
                    savings_value = float(estimated_savings.get('value', 0))
                    
                    if savings_value > max_savings:
                        max_savings = savings_value
                        best_option = option
                
                if best_option:
                    recommendations.append({
                        'function_arn': function_arn,
                        'function_name': function_arn.split(':')[-1],
                        'current_memory': current_memory,
                        'recommended_memory': best_option['memorySize'],
                        'finding': recommendation['finding'],
                        'estimated_monthly_savings': max_savings,
                        'projected_utilization': best_option.get('projectedUtilizationMetrics', {}),
                        'current_utilization': recommendation.get('currentUtilizationMetrics', {})
                    })
        
        return recommendations
    
    def calculate_total_savings_potential(self) -> Dict:
        """Calculate total potential savings across all resources"""
        ec2_recommendations = self.get_ec2_recommendations()
        lambda_recommendations = self.get_lambda_recommendations()
        
        total_ec2_savings = sum(rec['estimated_monthly_savings'] for rec in ec2_recommendations)
        total_lambda_savings = sum(rec['estimated_monthly_savings'] for rec in lambda_recommendations)
        
        return {
            'ec2_instances': {
                'count': len(ec2_recommendations),
                'monthly_savings': round(total_ec2_savings, 2),
                'annual_savings': round(total_ec2_savings * 12, 2)
            },
            'lambda_functions': {
                'count': len(lambda_recommendations),
                'monthly_savings': round(total_lambda_savings, 2),
                'annual_savings': round(total_lambda_savings * 12, 2)
            },
            'total': {
                'monthly_savings': round(total_ec2_savings + total_lambda_savings, 2),
                'annual_savings': round((total_ec2_savings + total_lambda_savings) * 12, 2)
            }
        }
```

## 🖥️ EC2 Rightsizing Strategies

### Systematic Rightsizing Approach
```yaml
rightsizing_methodology:
  phase_1_assessment:
    duration: "2 weeks"
    activities:
      - Enable detailed monitoring
      - Install CloudWatch agent for memory metrics
      - Collect baseline performance data
      - Identify optimization candidates
      
  phase_2_analysis:
    duration: "1 week"
    activities:
      - Analyze Compute Optimizer recommendations
      - Review application performance requirements
      - Calculate cost-benefit analysis
      - Prioritize instances for rightsizing
      
  phase_3_implementation:
    duration: "4 weeks"
    activities:
      - Start with non-production environments
      - Implement changes during maintenance windows
      - Monitor performance post-change
      - Rollback if performance issues occur
      
  phase_4_optimization:
    duration: "Ongoing"
    activities:
      - Continuous monitoring
      - Regular recommendation reviews
      - Automated rightsizing where possible
      - Performance validation
```

### Automated Rightsizing Implementation
```python
import boto3
import json
from datetime import datetime
import time

class AutomatedRightsizer:
    def __init__(self):
        self.ec2 = boto3.client('ec2')
        self.compute_optimizer = boto3.client('compute-optimizer')
        self.sns = boto3.client('sns')
        
    def rightsize_instance(self, instance_id: str, new_instance_type: str, 
                          dry_run: bool = True) -> Dict:
        """Rightsize an EC2 instance"""
        try:
            # Get current instance details
            instances = self.ec2.describe_instances(InstanceIds=[instance_id])
            instance = instances['Reservations'][0]['Instances'][0]
            current_type = instance['InstanceType']
            current_state = instance['State']['Name']
            
            if current_state != 'running':
                return {
                    'success': False,
                    'message': f"Instance {instance_id} is not running (state: {current_state})"
                }
            
            # Create snapshot before modification (safety measure)
            snapshot_id = self.create_instance_snapshot(instance_id)
            
            if dry_run:
                return {
                    'success': True,
                    'message': f"DRY RUN: Would change {instance_id} from {current_type} to {new_instance_type}",
                    'snapshot_id': snapshot_id
                }
            
            # Stop instance
            self.ec2.stop_instances(InstanceIds=[instance_id])
            
            # Wait for instance to stop
            waiter = self.ec2.get_waiter('instance_stopped')
            waiter.wait(InstanceIds=[instance_id])
            
            # Modify instance type
            self.ec2.modify_instance_attribute(
                InstanceId=instance_id,
                InstanceType={'Value': new_instance_type}
            )
            
            # Start instance
            self.ec2.start_instances(InstanceIds=[instance_id])
            
            # Wait for instance to start
            waiter = self.ec2.get_waiter('instance_running')
            waiter.wait(InstanceIds=[instance_id])
            
            return {
                'success': True,
                'message': f"Successfully changed {instance_id} from {current_type} to {new_instance_type}",
                'snapshot_id': snapshot_id,
                'old_type': current_type,
                'new_type': new_instance_type
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f"Error rightsizing {instance_id}: {str(e)}"
            }
    
    def create_instance_snapshot(self, instance_id: str) -> str:
        """Create EBS snapshot before rightsizing"""
        # Get instance volumes
        instance = self.ec2.describe_instances(InstanceIds=[instance_id])
        volumes = []
        
        for reservation in instance['Reservations']:
            for inst in reservation['Instances']:
                for bdm in inst.get('BlockDeviceMappings', []):
                    if 'Ebs' in bdm:
                        volumes.append(bdm['Ebs']['VolumeId'])
        
        # Create snapshots
        snapshot_ids = []
        for volume_id in volumes:
            snapshot = self.ec2.create_snapshot(
                VolumeId=volume_id,
                Description=f"Pre-rightsizing snapshot for {instance_id} - {datetime.now().isoformat()}"
            )
            snapshot_ids.append(snapshot['SnapshotId'])
        
        return snapshot_ids
    
    def batch_rightsize_instances(self, recommendations: List[Dict], 
                                 dry_run: bool = True) -> List[Dict]:
        """Batch rightsize multiple instances"""
        results = []
        
        for rec in recommendations:
            # Only process low-risk recommendations
            if rec.get('performance_risk', 'High') in ['VeryLow', 'Low']:
                result = self.rightsize_instance(
                    rec['instance_id'],
                    rec['recommended_type'],
                    dry_run
                )
                results.append({
                    'instance_id': rec['instance_id'],
                    'recommendation': rec,
                    'result': result
                })
                
                # Add delay between operations
                time.sleep(30)
        
        return results
```

## ⚡ Lambda Function Optimization

### Lambda Cost Optimization Framework
```yaml
lambda_optimization:
  memory_optimization:
    approach: "Power tuning to find optimal memory/cost balance"
    tools: ["AWS Lambda Power Tuning", "Custom benchmarking"]
    metrics: ["Duration", "Cost", "Memory utilization"]
    
  execution_optimization:
    cold_starts:
      - Use provisioned concurrency for critical functions
      - Optimize initialization code
      - Consider container reuse patterns
      
    runtime_efficiency:
      - Choose appropriate runtime
      - Optimize dependencies and imports
      - Use connection pooling
      - Implement efficient algorithms
      
  cost_monitoring:
    metrics:
      - Invocation count
      - Duration
      - Memory utilization
      - Error rate
      - Throttles
```

### Lambda Power Tuning Implementation
```python
import boto3
import json
import time
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict

class LambdaPowerTuner:
    def __init__(self):
        self.lambda_client = boto3.client('lambda')
        self.cloudwatch = boto3.client('cloudwatch')
        
    def get_function_metrics(self, function_name: str, days: int = 7) -> Dict:
        """Get CloudWatch metrics for Lambda function"""
        from datetime import datetime, timedelta
        
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)
        
        metrics = {}
        
        # Get invocation count
        invocations = self.cloudwatch.get_metric_statistics(
            Namespace='AWS/Lambda',
            MetricName='Invocations',
            Dimensions=[{'Name': 'FunctionName', 'Value': function_name}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Sum']
        )
        
        # Get duration
        duration = self.cloudwatch.get_metric_statistics(
            Namespace='AWS/Lambda',
            MetricName='Duration',
            Dimensions=[{'Name': 'FunctionName', 'Value': function_name}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Average']
        )
        
        # Get errors
        errors = self.cloudwatch.get_metric_statistics(
            Namespace='AWS/Lambda',
            MetricName='Errors',
            Dimensions=[{'Name': 'FunctionName', 'Value': function_name}],
            StartTime=start_time,
            EndTime=end_time,
            Period=3600,
            Statistics=['Sum']
        )
        
        total_invocations = sum(dp['Sum'] for dp in invocations['Datapoints'])
        avg_duration = sum(dp['Average'] for dp in duration['Datapoints']) / len(duration['Datapoints']) if duration['Datapoints'] else 0
        total_errors = sum(dp['Sum'] for dp in errors['Datapoints'])
        
        return {
            'total_invocations': total_invocations,
            'average_duration_ms': round(avg_duration, 2),
            'total_errors': total_errors,
            'error_rate': round((total_errors / total_invocations * 100), 2) if total_invocations > 0 else 0
        }
    
    def test_memory_configuration(self, function_name: str, memory_size: int, 
                                test_payload: Dict, iterations: int = 10) -> Dict:
        """Test Lambda function with specific memory configuration"""
        # Update function memory
        self.lambda_client.update_function_configuration(
            FunctionName=function_name,
            MemorySize=memory_size
        )
        
        # Wait for update to complete
        time.sleep(10)
        
        durations = []
        costs = []
        
        for i in range(iterations):
            start_time = time.time()
            
            response = self.lambda_client.invoke(
                FunctionName=function_name,
                Payload=json.dumps(test_payload)
            )
            
            # Parse response
            result = json.loads(response['Payload'].read())
            
            if response['StatusCode'] == 200:
                # Calculate duration and cost
                duration = response.get('Duration', 0)  # This would come from response metadata
                
                # Calculate cost (simplified)
                # Actual cost calculation: (memory/1024) * duration * price_per_gb_second
                gb_seconds = (memory_size / 1024) * (duration / 1000)
                cost = gb_seconds * 0.0000166667  # Current Lambda pricing
                
                durations.append(duration)
                costs.append(cost)
            
            time.sleep(1)  # Small delay between invocations
        
        avg_duration = sum(durations) / len(durations) if durations else 0
        avg_cost = sum(costs) / len(costs) if costs else 0
        
        return {
            'memory_size': memory_size,
            'average_duration_ms': round(avg_duration, 2),
            'average_cost_usd': round(avg_cost, 6),
            'success_rate': len(durations) / iterations * 100
        }
    
    def find_optimal_memory(self, function_name: str, test_payload: Dict) -> Dict:
        """Find optimal memory configuration for Lambda function"""
        memory_sizes = [128, 256, 512, 1024, 1536, 2048, 3008]
        results = []
        
        original_config = self.lambda_client.get_function_configuration(
            FunctionName=function_name
        )
        original_memory = original_config['MemorySize']
        
        try:
            for memory_size in memory_sizes:
                print(f"Testing memory size: {memory_size}MB")
                result = self.test_memory_configuration(
                    function_name, memory_size, test_payload
                )
                results.append(result)
        
        finally:
            # Restore original memory configuration
            self.lambda_client.update_function_configuration(
                FunctionName=function_name,
                MemorySize=original_memory
            )
        
        # Find optimal configuration (lowest cost with acceptable performance)
        optimal = min(results, key=lambda x: x['average_cost_usd'])
        
        return {
            'function_name': function_name,
            'original_memory': original_memory,
            'optimal_memory': optimal['memory_size'],
            'cost_reduction': round(
                (results[0]['average_cost_usd'] - optimal['average_cost_usd']) / results[0]['average_cost_usd'] * 100, 2
            ),
            'all_results': results,
            'recommendation': optimal
        }
```

## ☸️ Kubernetes Resource Management

### Resource Requests and Limits Best Practices
```yaml
# Properly configured Kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: optimized-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: optimized-app
  template:
    metadata:
      labels:
        app: optimized-app
    spec:
      containers:
        - name: app
          image: myapp:latest
          
          # Properly set resource requests and limits
          resources:
            requests:
              cpu: 100m      # Minimum CPU needed
              memory: 128Mi  # Minimum memory needed
            limits:
              cpu: 500m      # Maximum CPU allowed
              memory: 512Mi  # Maximum memory allowed
          
          # Liveness and readiness probes
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
            
          readinessProbe:
            httpGet:
              path: /ready
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
          
          # Environment-specific configuration
          env:
            - name: ENVIRONMENT
              value: "production"
            - name: LOG_LEVEL
              value: "info"
      
      # Quality of Service class
      # This deployment will get "Burstable" QoS due to requests < limits
```

### Goldilocks for Right-sizing Recommendations
```yaml
# Goldilocks VPA recommender configuration
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: optimized-app-vpa
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: optimized-app
  updatePolicy:
    updateMode: "Off"  # Only provide recommendations, don't auto-update
  resourcePolicy:
    containerPolicies:
      - containerName: app
        minAllowed:
          cpu: 50m
          memory: 64Mi
        maxAllowed:
          cpu: 1000m
          memory: 1Gi
        controlledResources: ["cpu", "memory"]
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Do you understand how to use AWS Compute Optimizer effectively?
- [ ] Can you implement automated rightsizing strategies?
- [ ] Do you know how to optimize Lambda functions for cost?
- [ ] Can you set proper Kubernetes resource requests and limits?

### Practical Skills
- [ ] Can you analyze and implement rightsizing recommendations?
- [ ] Do you know how to perform Lambda power tuning?
- [ ] Can you set up automated rightsizing workflows?
- [ ] Do you understand the risks and benefits of rightsizing?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete rightsizing automation scripts
- Lambda power tuning implementations
- Kubernetes resource optimization configs
- Monitoring and alerting setups

## ➡️ Next Steps
Continue to [Module 08: Reserved Instances and Savings Plans](../08_reserved_instances_and_savings_plans/)

---
**Module Duration**: 1 week  
**Difficulty**: Intermediate  
**Prerequisites**: Module 06 completion
