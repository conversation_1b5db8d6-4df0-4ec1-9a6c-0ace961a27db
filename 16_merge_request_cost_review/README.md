# 📘 Module 16: Merge Request Cost Review

## 🎯 Learning Goals
By the end of this module, you will:
- Implement automated cost impact analysis in CI/CD pipelines
- Set up Infracost integration for pull request reviews
- Create custom cost review workflows for different environments
- Build cost approval gates for infrastructure changes
- Integrate cost analysis with GitLab, GitHub, and other platforms

## 📚 Table of Contents
1. [CI/CD Cost Impact Analysis](#cicd-cost-impact-analysis)
2. [Infracost Integration](#infracost-integration)
3. [Custom Cost Review Workflows](#custom-cost-review-workflows)
4. [Cost Approval Gates](#cost-approval-gates)
5. [Platform Integrations](#platform-integrations)
6. [Study Checkpoints](#study-checkpoints)
7. [Practical Examples](#practical-examples)

## 🔄 CI/CD Cost Impact Analysis

### GitHub Actions Cost Review
```yaml
# .github/workflows/cost-review.yml
name: Infrastructure Cost Review
on:
  pull_request:
    paths:
      - 'terraform/**'
      - 'cloudformation/**'
      - '.github/workflows/cost-review.yml'

jobs:
  cost-analysis:
    name: Cost Impact Analysis
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      id-token: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
          aws-region: us-east-1

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.6.0

      - name: Setup Infracost
        uses: infracost/actions/setup@v2
        with:
          api-key: ${{ secrets.INFRACOST_API_KEY }}

      - name: Checkout base branch
        run: |
          git checkout ${{ github.event.pull_request.base.ref }}

      - name: Generate Infracost baseline
        run: |
          cd terraform
          terraform init
          terraform plan -out=baseline.tfplan
          infracost breakdown --path=baseline.tfplan \
                              --format=json \
                              --out-file=/tmp/infracost-base.json

      - name: Checkout PR branch
        run: |
          git checkout ${{ github.event.pull_request.head.ref }}

      - name: Generate Infracost diff
        run: |
          cd terraform
          terraform init
          terraform plan -out=current.tfplan
          infracost diff --path=current.tfplan \
                        --compare-to=/tmp/infracost-base.json \
                        --format=json \
                        --out-file=/tmp/infracost.json

      - name: Custom cost analysis
        run: |
          python .github/scripts/cost-analysis.py \
            --infracost-file=/tmp/infracost.json \
            --pr-number=${{ github.event.pull_request.number }} \
            --output-file=/tmp/cost-analysis.json

      - name: Post cost review comment
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const costData = JSON.parse(fs.readFileSync('/tmp/cost-analysis.json', 'utf8'));
            
            const comment = `## 💰 Infrastructure Cost Impact Analysis
            
            **Monthly Cost Change:** ${costData.monthlyCostChange >= 0 ? '+' : ''}$${costData.monthlyCostChange.toFixed(2)}
            **Annual Cost Impact:** ${costData.annualCostChange >= 0 ? '+' : ''}$${costData.annualCostChange.toFixed(2)}
            
            ### 📊 Cost Breakdown by Service
            ${costData.serviceBreakdown.map(service => 
              `- **${service.name}:** ${service.change >= 0 ? '+' : ''}$${service.change.toFixed(2)}/month`
            ).join('\n')}
            
            ### 🎯 Recommendations
            ${costData.recommendations.map(rec => `- ${rec}`).join('\n')}
            
            ### ⚠️ Cost Approval Status
            ${costData.requiresApproval ? '❌ **Requires FinOps approval** (cost increase > $500/month)' : '✅ **Auto-approved** (within cost thresholds)'}
            
            <details>
            <summary>📈 Detailed Cost Analysis</summary>
            
            \`\`\`json
            ${JSON.stringify(costData.detailedAnalysis, null, 2)}
            \`\`\`
            </details>
            
            ---
            *Generated by AWS Cost Review Bot | [View in Infracost](https://dashboard.infracost.io)*`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

      - name: Check cost approval gate
        run: |
          python .github/scripts/cost-approval-gate.py \
            --cost-analysis-file=/tmp/cost-analysis.json \
            --approval-threshold=500 \
            --fail-on-threshold-exceeded=true
```

### Custom Cost Analysis Script
```python
#!/usr/bin/env python3
# .github/scripts/cost-analysis.py

import json
import argparse
import sys
from typing import Dict, List

class CostAnalyzer:
    def __init__(self):
        self.approval_thresholds = {
            'low': 100,      # $100/month - auto-approve
            'medium': 500,   # $500/month - requires team lead approval
            'high': 2000,    # $2000/month - requires FinOps approval
            'critical': 5000 # $5000/month - requires executive approval
        }
        
    def analyze_infracost_output(self, infracost_file: str) -> Dict:
        """Analyze Infracost output and generate comprehensive cost analysis"""
        
        with open(infracost_file, 'r') as f:
            infracost_data = json.load(f)
        
        analysis = {
            'monthlyCostChange': 0,
            'annualCostChange': 0,
            'serviceBreakdown': [],
            'recommendations': [],
            'requiresApproval': False,
            'approvalLevel': 'auto',
            'detailedAnalysis': {}
        }
        
        # Extract cost changes
        if 'diffTotalMonthlyCost' in infracost_data:
            monthly_change = float(infracost_data['diffTotalMonthlyCost'])
            analysis['monthlyCostChange'] = monthly_change
            analysis['annualCostChange'] = monthly_change * 12
        
        # Analyze by service/resource type
        analysis['serviceBreakdown'] = self._analyze_service_breakdown(infracost_data)
        
        # Generate recommendations
        analysis['recommendations'] = self._generate_recommendations(infracost_data, analysis)
        
        # Determine approval requirements
        approval_info = self._determine_approval_requirements(analysis['monthlyCostChange'])
        analysis.update(approval_info)
        
        # Add detailed analysis
        analysis['detailedAnalysis'] = self._create_detailed_analysis(infracost_data)
        
        return analysis
    
    def _analyze_service_breakdown(self, infracost_data: Dict) -> List[Dict]:
        """Break down cost changes by AWS service"""
        service_costs = {}
        
        # Process projects and resources
        for project in infracost_data.get('projects', []):
            for resource in project.get('diff', {}).get('resources', []):
                resource_type = resource.get('name', '').split('.')[0]
                service_name = self._map_resource_to_service(resource_type)
                
                monthly_cost_diff = 0
                if 'monthlyCostDiff' in resource:
                    monthly_cost_diff = float(resource['monthlyCostDiff'])
                
                if service_name not in service_costs:
                    service_costs[service_name] = 0
                service_costs[service_name] += monthly_cost_diff
        
        # Convert to list and sort by impact
        breakdown = [
            {'name': service, 'change': cost}
            for service, cost in service_costs.items()
            if abs(cost) > 0.01  # Only include meaningful changes
        ]
        
        return sorted(breakdown, key=lambda x: abs(x['change']), reverse=True)
    
    def _map_resource_to_service(self, resource_type: str) -> str:
        """Map Terraform resource types to AWS service names"""
        service_mapping = {
            'aws_instance': 'EC2',
            'aws_db_instance': 'RDS',
            'aws_s3_bucket': 'S3',
            'aws_lambda_function': 'Lambda',
            'aws_ecs_service': 'ECS',
            'aws_eks_cluster': 'EKS',
            'aws_nat_gateway': 'NAT Gateway',
            'aws_lb': 'Load Balancer',
            'aws_ebs_volume': 'EBS',
            'aws_cloudfront_distribution': 'CloudFront'
        }
        
        return service_mapping.get(resource_type, 'Other')
    
    def _generate_recommendations(self, infracost_data: Dict, analysis: Dict) -> List[str]:
        """Generate cost optimization recommendations"""
        recommendations = []
        monthly_change = analysis['monthlyCostChange']
        
        # High cost increase recommendations
        if monthly_change > 1000:
            recommendations.append("Consider implementing Reserved Instances or Savings Plans for new compute resources")
            recommendations.append("Review instance types and sizes for optimization opportunities")
        
        # Analyze specific resource types
        for service in analysis['serviceBreakdown']:
            if service['change'] > 500:
                if service['name'] == 'EC2':
                    recommendations.append(f"EC2 costs increasing by ${service['change']:.2f}/month - consider rightsizing or Spot instances")
                elif service['name'] == 'RDS':
                    recommendations.append(f"RDS costs increasing by ${service['change']:.2f}/month - review instance classes and storage optimization")
                elif service['name'] == 'NAT Gateway':
                    recommendations.append(f"NAT Gateway costs increasing - consider VPC endpoints for AWS services")
        
        # General recommendations
        if monthly_change > 0:
            recommendations.append("Implement cost monitoring and alerting for new resources")
            recommendations.append("Consider using AWS Budgets to track spending on new infrastructure")
        
        return recommendations
    
    def _determine_approval_requirements(self, monthly_change: float) -> Dict:
        """Determine what level of approval is required"""
        abs_change = abs(monthly_change)
        
        if abs_change >= self.approval_thresholds['critical']:
            return {
                'requiresApproval': True,
                'approvalLevel': 'executive',
                'approvalReason': f'Cost change exceeds ${self.approval_thresholds["critical"]}/month threshold'
            }
        elif abs_change >= self.approval_thresholds['high']:
            return {
                'requiresApproval': True,
                'approvalLevel': 'finops',
                'approvalReason': f'Cost change exceeds ${self.approval_thresholds["high"]}/month threshold'
            }
        elif abs_change >= self.approval_thresholds['medium']:
            return {
                'requiresApproval': True,
                'approvalLevel': 'team_lead',
                'approvalReason': f'Cost change exceeds ${self.approval_thresholds["medium"]}/month threshold'
            }
        else:
            return {
                'requiresApproval': False,
                'approvalLevel': 'auto',
                'approvalReason': 'Cost change within auto-approval threshold'
            }

def main():
    parser = argparse.ArgumentParser(description='Analyze infrastructure cost changes')
    parser.add_argument('--infracost-file', required=True, help='Path to Infracost JSON output')
    parser.add_argument('--pr-number', required=True, help='Pull request number')
    parser.add_argument('--output-file', required=True, help='Output file for analysis results')
    
    args = parser.parse_args()
    
    analyzer = CostAnalyzer()
    analysis = analyzer.analyze_infracost_output(args.infracost_file)
    
    # Add PR context
    analysis['prNumber'] = args.pr_number
    analysis['timestamp'] = datetime.now().isoformat()
    
    # Write analysis to output file
    with open(args.output_file, 'w') as f:
        json.dump(analysis, f, indent=2)
    
    print(f"Cost analysis completed. Monthly change: ${analysis['monthlyCostChange']:.2f}")
    print(f"Approval required: {analysis['requiresApproval']}")

if __name__ == '__main__':
    main()
```

## 🚪 Cost Approval Gates

### Approval Gate Script
```python
#!/usr/bin/env python3
# .github/scripts/cost-approval-gate.py

import json
import argparse
import sys
import os

class CostApprovalGate:
    def __init__(self):
        self.github_token = os.environ.get('GITHUB_TOKEN')
        
    def check_approval_gate(self, cost_analysis_file: str, 
                          approval_threshold: float,
                          fail_on_threshold_exceeded: bool = True) -> bool:
        """Check if cost changes require approval and enforce gates"""
        
        with open(cost_analysis_file, 'r') as f:
            analysis = json.load(f)
        
        monthly_change = abs(analysis.get('monthlyCostChange', 0))
        requires_approval = analysis.get('requiresApproval', False)
        approval_level = analysis.get('approvalLevel', 'auto')
        
        print(f"Monthly cost change: ${monthly_change:.2f}")
        print(f"Requires approval: {requires_approval}")
        print(f"Approval level: {approval_level}")
        
        if not requires_approval:
            print("✅ Cost change approved automatically")
            return True
        
        # Check for approval labels or comments
        approval_status = self._check_existing_approvals(analysis, approval_level)
        
        if approval_status['approved']:
            print(f"✅ Cost change approved by {approval_status['approver']}")
            return True
        else:
            print(f"❌ Cost change requires {approval_level} approval")
            
            if fail_on_threshold_exceeded:
                print("Failing pipeline due to missing approval")
                return False
            else:
                print("Warning: Proceeding without approval (fail_on_threshold_exceeded=false)")
                return True
    
    def _check_existing_approvals(self, analysis: Dict, required_level: str) -> Dict:
        """Check if required approvals exist in PR"""
        # This would integrate with GitHub API to check for:
        # - Approval labels
        # - Review approvals from authorized users
        # - Special approval comments
        
        # Simplified implementation
        return {
            'approved': False,
            'approver': None,
            'approval_method': None
        }

def main():
    parser = argparse.ArgumentParser(description='Enforce cost approval gates')
    parser.add_argument('--cost-analysis-file', required=True)
    parser.add_argument('--approval-threshold', type=float, default=500)
    parser.add_argument('--fail-on-threshold-exceeded', type=bool, default=True)
    
    args = parser.parse_args()
    
    gate = CostApprovalGate()
    approved = gate.check_approval_gate(
        args.cost_analysis_file,
        args.approval_threshold,
        args.fail_on_threshold_exceeded
    )
    
    sys.exit(0 if approved else 1)

if __name__ == '__main__':
    main()
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Do you understand how to integrate cost analysis into CI/CD pipelines?
- [ ] Can you implement automated cost review workflows?
- [ ] Do you know how to create cost approval gates?
- [ ] Can you integrate with different version control platforms?

### Practical Skills
- [ ] Can you set up Infracost for pull request reviews?
- [ ] Do you know how to create custom cost analysis scripts?
- [ ] Can you implement approval workflows for cost changes?
- [ ] Do you understand how to measure the effectiveness of cost reviews?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete CI/CD pipeline configurations
- Custom cost analysis implementations
- Approval workflow templates
- Platform integration examples

## ➡️ Next Steps
Continue to [Module 17: FinOps Study Plan and Certifications](../17_finops_study_plan_and_certifications/)

---
**Module Duration**: 1 week  
**Difficulty**: Expert  
**Prerequisites**: Module 15 completion
