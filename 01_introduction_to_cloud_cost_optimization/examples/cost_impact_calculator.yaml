# Cost Impact Calculator Template
# Use this template to calculate potential savings from cost optimization initiatives

cost_optimization_calculator:
  current_state:
    monthly_cloud_spend: 50000  # USD
    waste_percentage: 30        # Typical waste percentage
    
  optimization_targets:
    compute:
      current_spend: 20000      # USD/month
      optimization_potential: 25  # Percentage
      estimated_savings: 5000   # USD/month
      
    storage:
      current_spend: 8000       # USD/month
      optimization_potential: 40  # Percentage
      estimated_savings: 3200   # USD/month
      
    network:
      current_spend: 5000       # USD/month
      optimization_potential: 20  # Percentage
      estimated_savings: 1000   # USD/month
      
    unused_resources:
      current_spend: 7000       # USD/month
      optimization_potential: 80  # Percentage
      estimated_savings: 5600   # USD/month

  total_potential_savings:
    monthly: 14800              # USD/month
    annual: 177600              # USD/year
    percentage: 29.6            # Percentage of total spend

  roi_analysis:
    optimization_investment:
      tooling_costs: 2000       # USD/month
      engineering_time: 5000    # USD/month (FTE cost)
      training_costs: 1000      # USD/month (amortized)
      total_investment: 8000    # USD/month
      
    net_savings:
      monthly: 6800             # USD/month (savings - investment)
      annual: 81600             # USD/year
      roi_percentage: 85        # ROI percentage
      payback_period: 1.2       # Months

  implementation_timeline:
    phase_1:
      duration: "1-2 months"
      focus: "Quick wins (unused resources)"
      expected_savings: 5600    # USD/month
      
    phase_2:
      duration: "2-3 months"
      focus: "Rightsizing and storage optimization"
      expected_savings: 8200    # USD/month
      
    phase_3:
      duration: "3-6 months"
      focus: "Advanced optimization and automation"
      expected_savings: 14800   # USD/month

# Usage Instructions:
# 1. Replace the example values with your actual cloud spend data
# 2. Adjust optimization potential percentages based on your assessment
# 3. Calculate ROI to justify optimization investments
# 4. Use this data to build business case for FinOps initiatives
