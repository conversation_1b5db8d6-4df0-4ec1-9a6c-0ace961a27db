# Comprehensive Tagging Strategy for FinOps
# This template provides a complete tagging framework for cost allocation and governance

tagging_strategy:
  # Core Required Tags (Must be present on all resources)
  required_tags:
    Environment:
      description: "Deployment environment"
      values: ["prod", "staging", "dev", "test"]
      enforcement: "SCP Policy"
      cost_allocation: true
      
    Team:
      description: "Owning team or department"
      values: ["backend", "frontend", "data", "devops", "security"]
      enforcement: "SCP Policy"
      cost_allocation: true
      
    Project:
      description: "Project or product name"
      format: "lowercase-with-hyphens"
      examples: ["user-auth", "payment-service", "analytics-platform"]
      enforcement: "SCP Policy"
      cost_allocation: true
      
    CostCenter:
      description: "Financial cost center"
      values: ["engineering", "marketing", "sales", "operations"]
      enforcement: "SCP Policy"
      cost_allocation: true
      
    Owner:
      description: "Technical owner email"
      format: "<EMAIL>"
      enforcement: "SCP Policy"
      cost_allocation: false

  # Optional Tags (Recommended for enhanced visibility)
  optional_tags:
    Application:
      description: "Application or service name"
      examples: ["web-app", "api-gateway", "database"]
      cost_allocation: true
      
    Version:
      description: "Application or infrastructure version"
      format: "semantic versioning (v1.2.3)"
      cost_allocation: false
      
    Schedule:
      description: "Resource usage schedule"
      values: ["24x7", "business-hours", "weekend-only", "on-demand"]
      cost_allocation: false
      automation_trigger: true
      
    Backup:
      description: "Backup requirements"
      values: ["daily", "weekly", "monthly", "none"]
      cost_allocation: false
      
    Compliance:
      description: "Compliance requirements"
      values: ["pci", "hipaa", "sox", "gdpr", "none"]
      cost_allocation: false

  # Automation Tags (Used by automation systems)
  automation_tags:
    AutoShutdown:
      description: "Automatic shutdown schedule"
      values: ["enabled", "disabled"]
      automation_trigger: true
      
    AutoScale:
      description: "Auto-scaling configuration"
      values: ["enabled", "disabled", "scheduled"]
      automation_trigger: true
      
    Monitoring:
      description: "Monitoring level"
      values: ["basic", "enhanced", "custom"]
      automation_trigger: true

# Tag Enforcement Policies
enforcement_policies:
  service_control_policy:
    name: "RequiredTagsPolicy"
    description: "Enforce required tags on resource creation"
    policy_document: |
      {
        "Version": "2012-10-17",
        "Statement": [
          {
            "Effect": "Deny",
            "Action": [
              "ec2:RunInstances",
              "rds:CreateDBInstance",
              "s3:CreateBucket"
            ],
            "Resource": "*",
            "Condition": {
              "Null": {
                "aws:RequestedRegion": "false",
                "aws:RequestTag/Environment": "true",
                "aws:RequestTag/Team": "true",
                "aws:RequestTag/Project": "true",
                "aws:RequestTag/CostCenter": "true",
                "aws:RequestTag/Owner": "true"
              }
            }
          }
        ]
      }

  lambda_tag_enforcement:
    name: "AutoTagResources"
    description: "Automatically tag resources missing required tags"
    trigger: "CloudWatch Events"
    runtime: "python3.9"

# Cost Allocation Rules
cost_allocation:
  primary_dimensions:
    - Environment
    - Team
    - Project
    - CostCenter
    
  allocation_hierarchy:
    level_1: CostCenter
    level_2: Team
    level_3: Project
    level_4: Environment
    
  shared_services_allocation:
    method: "proportional"
    basis: "resource_usage"
    tags_used: ["Team", "Project"]

# Tagging Automation
automation:
  auto_tagging_rules:
    ec2_instances:
      - tag: "CreatedBy"
        value: "${aws:userid}"
        
      - tag: "CreatedDate"
        value: "${aws:CurrentTime}"
        
      - tag: "LaunchedBy"
        value: "${aws:username}"
        
    s3_buckets:
      - tag: "DataClassification"
        value: "internal"  # default
        
      - tag: "RetentionPeriod"
        value: "7years"    # default

  tag_inheritance:
    - parent: "VPC"
      children: ["Subnet", "SecurityGroup", "RouteTable"]
      inherited_tags: ["Environment", "Team", "Project"]
      
    - parent: "AutoScalingGroup"
      children: ["EC2Instance"]
      inherited_tags: ["Environment", "Team", "Project", "Application"]

# Monitoring and Compliance
monitoring:
  tag_compliance_metrics:
    - metric: "TagCompliancePercentage"
      target: 95
      alert_threshold: 90
      
    - metric: "UntaggedResourceCount"
      target: 0
      alert_threshold: 10
      
  reporting:
    frequency: "daily"
    recipients: ["<EMAIL>"]
    dashboard: "TagComplianceDashboard"

# Implementation Phases
implementation_phases:
  phase_1:
    duration: "2 weeks"
    scope: "Core required tags"
    activities:
      - Define tagging standards
      - Create SCP policies
      - Implement tag enforcement
      
  phase_2:
    duration: "4 weeks"
    scope: "Existing resource tagging"
    activities:
      - Audit existing resources
      - Bulk tag existing resources
      - Implement automation
      
  phase_3:
    duration: "2 weeks"
    scope: "Monitoring and optimization"
    activities:
      - Set up compliance monitoring
      - Create cost allocation reports
      - Train teams on tagging

# Best Practices
best_practices:
  naming_conventions:
    - Use lowercase letters
    - Use hyphens for separation
    - Keep names concise but descriptive
    - Avoid special characters
    
  tag_values:
    - Use consistent value formats
    - Avoid spaces in tag values
    - Use predefined value lists where possible
    - Document all tag meanings
    
  governance:
    - Regular tag audits
    - Automated compliance checking
    - Team training and documentation
    - Continuous improvement process
