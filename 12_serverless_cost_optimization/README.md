# 📘 Module 12: Serverless Cost Optimization

## 🎯 Learning Goals
By the end of this module, you will:
- Master Lambda memory and execution time optimization
- Optimize Step Functions and API Gateway costs
- Implement cost-effective serverless architectures
- Monitor and analyze serverless application costs
- Leverage both free and premium tools for serverless optimization

## 📚 Table of Contents
1. [Lambda Cost Optimization](#lambda-cost-optimization)
2. [Step Functions Optimization](#step-functions-optimization)
3. [API Gateway Cost Controls](#api-gateway-cost-controls)
4. [Serverless Architecture Patterns](#serverless-architecture-patterns)
5. [Monitoring and Analytics](#monitoring-and-analytics)
6. [Tools and Integration](#tools-and-integration)
7. [Study Checkpoints](#study-checkpoints)
8. [Practical Examples](#practical-examples)

## ⚡ Lambda Cost Optimization

### Lambda Pricing Model
```yaml
lambda_pricing:
  request_charges:
    first_1m_requests: "Free"
    additional_requests: "$0.20 per 1M requests"
    
  duration_charges:
    calculation: "GB-seconds of execution time"
    price_per_gb_second: "$0.0000166667"
    free_tier: "400,000 GB-seconds per month"
    
  provisioned_concurrency:
    price_per_gb_hour: "$0.0000097222"
    request_price: "$0.20 per 1M requests"
    
  cost_factors:
    - "Memory allocation (128MB - 10,240MB)"
    - "Execution duration"
    - "Number of invocations"
    - "Provisioned concurrency usage"
```

### Advanced Lambda Power Tuning
```python
import boto3
import json
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Tuple

class AdvancedLambdaPowerTuner:
    def __init__(self):
        self.lambda_client = boto3.client('lambda')
        self.cloudwatch = boto3.client('cloudwatch')
        
    def comprehensive_power_tuning(self, function_name: str, 
                                 test_payloads: List[Dict],
                                 memory_sizes: List[int] = None,
                                 iterations: int = 10) -> Dict:
        """Comprehensive power tuning with multiple test scenarios"""
        
        if memory_sizes is None:
            memory_sizes = [128, 256, 512, 1024, 1536, 2048, 3008]
        
        original_config = self.lambda_client.get_function_configuration(
            FunctionName=function_name
        )
        original_memory = original_config['MemorySize']
        
        results = {
            'function_name': function_name,
            'original_memory': original_memory,
            'test_scenarios': [],
            'optimal_configurations': {},
            'cost_analysis': {}
        }
        
        try:
            for scenario_idx, payload in enumerate(test_payloads):
                scenario_name = f"scenario_{scenario_idx + 1}"
                scenario_results = []
                
                print(f"Testing scenario {scenario_idx + 1}/{len(test_payloads)}")
                
                for memory_size in memory_sizes:
                    print(f"  Testing memory: {memory_size}MB")
                    
                    # Update function memory
                    self._update_function_memory(function_name, memory_size)
                    
                    # Run performance tests
                    perf_results = self._run_performance_tests(
                        function_name, payload, iterations
                    )
                    
                    # Calculate costs
                    cost_analysis = self._calculate_detailed_costs(
                        memory_size, perf_results
                    )
                    
                    scenario_results.append({
                        'memory_size': memory_size,
                        'performance': perf_results,
                        'cost_analysis': cost_analysis
                    })
                
                # Find optimal configuration for this scenario
                optimal = self._find_optimal_configuration(scenario_results)
                
                results['test_scenarios'].append({
                    'scenario_name': scenario_name,
                    'payload': payload,
                    'results': scenario_results,
                    'optimal_config': optimal
                })
                
                results['optimal_configurations'][scenario_name] = optimal
        
        finally:
            # Restore original configuration
            self._update_function_memory(function_name, original_memory)
        
        # Generate overall recommendations
        results['recommendations'] = self._generate_optimization_recommendations(results)
        
        return results
    
    def _update_function_memory(self, function_name: str, memory_size: int):
        """Update Lambda function memory size"""
        self.lambda_client.update_function_configuration(
            FunctionName=function_name,
            MemorySize=memory_size
        )
        
        # Wait for update to complete
        waiter = self.lambda_client.get_waiter('function_updated')
        waiter.wait(FunctionName=function_name)
        time.sleep(5)  # Additional buffer
    
    def _run_performance_tests(self, function_name: str, 
                             payload: Dict, iterations: int) -> Dict:
        """Run performance tests with concurrent execution"""
        
        def invoke_function():
            start_time = time.time()
            
            response = self.lambda_client.invoke(
                FunctionName=function_name,
                Payload=json.dumps(payload),
                LogType='Tail'
            )
            
            end_time = time.time()
            client_duration = (end_time - start_time) * 1000  # Convert to ms
            
            # Parse response
            status_code = response['StatusCode']
            
            # Extract duration from logs (more accurate)
            if 'LogResult' in response:
                import base64
                log_data = base64.b64decode(response['LogResult']).decode('utf-8')
                
                # Extract billed duration from logs
                billed_duration = self._extract_billed_duration(log_data)
                memory_used = self._extract_memory_used(log_data)
            else:
                billed_duration = client_duration
                memory_used = None
            
            return {
                'status_code': status_code,
                'client_duration': client_duration,
                'billed_duration': billed_duration,
                'memory_used': memory_used,
                'success': status_code == 200
            }
        
        # Run concurrent invocations
        results = []
        with ThreadPoolExecutor(max_workers=min(iterations, 10)) as executor:
            futures = [executor.submit(invoke_function) for _ in range(iterations)]
            
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"Invocation failed: {e}")
        
        # Calculate statistics
        successful_results = [r for r in results if r['success']]
        
        if not successful_results:
            return {'error': 'No successful invocations'}
        
        durations = [r['billed_duration'] for r in successful_results]
        memory_used = [r['memory_used'] for r in successful_results if r['memory_used']]
        
        return {
            'total_invocations': len(results),
            'successful_invocations': len(successful_results),
            'success_rate': len(successful_results) / len(results) * 100,
            'duration_stats': {
                'mean': statistics.mean(durations),
                'median': statistics.median(durations),
                'min': min(durations),
                'max': max(durations),
                'std_dev': statistics.stdev(durations) if len(durations) > 1 else 0
            },
            'memory_stats': {
                'mean': statistics.mean(memory_used) if memory_used else None,
                'max': max(memory_used) if memory_used else None
            }
        }
    
    def _extract_billed_duration(self, log_data: str) -> float:
        """Extract billed duration from CloudWatch logs"""
        import re
        
        # Look for "Billed Duration: X ms" pattern
        pattern = r'Billed Duration: (\d+) ms'
        match = re.search(pattern, log_data)
        
        if match:
            return float(match.group(1))
        
        # Fallback: look for "Duration: X ms" pattern
        pattern = r'Duration: ([\d.]+) ms'
        match = re.search(pattern, log_data)
        
        if match:
            return float(match.group(1))
        
        return 0.0
    
    def _extract_memory_used(self, log_data: str) -> float:
        """Extract memory used from CloudWatch logs"""
        import re
        
        # Look for "Max Memory Used: X MB" pattern
        pattern = r'Max Memory Used: (\d+) MB'
        match = re.search(pattern, log_data)
        
        if match:
            return float(match.group(1))
        
        return None
    
    def _calculate_detailed_costs(self, memory_size: int, perf_results: Dict) -> Dict:
        """Calculate detailed cost analysis"""
        if 'error' in perf_results:
            return {'error': 'Cannot calculate costs due to performance test error'}
        
        # Lambda pricing constants
        price_per_request = 0.0000002  # $0.20 per 1M requests
        price_per_gb_second = 0.0000166667
        
        # Calculate GB-seconds
        memory_gb = memory_size / 1024
        avg_duration_seconds = perf_results['duration_stats']['mean'] / 1000
        gb_seconds_per_invocation = memory_gb * avg_duration_seconds
        
        # Calculate costs per invocation
        request_cost = price_per_request
        duration_cost = gb_seconds_per_invocation * price_per_gb_second
        total_cost_per_invocation = request_cost + duration_cost
        
        # Monthly cost estimates (assuming different invocation volumes)
        monthly_estimates = {}
        for monthly_invocations in [1000, 10000, 100000, 1000000]:
            monthly_cost = total_cost_per_invocation * monthly_invocations
            monthly_estimates[f"{monthly_invocations}_invocations"] = round(monthly_cost, 4)
        
        return {
            'memory_size_mb': memory_size,
            'memory_size_gb': memory_gb,
            'avg_duration_ms': perf_results['duration_stats']['mean'],
            'avg_duration_seconds': avg_duration_seconds,
            'gb_seconds_per_invocation': round(gb_seconds_per_invocation, 6),
            'cost_per_invocation': round(total_cost_per_invocation, 8),
            'cost_breakdown': {
                'request_cost': round(request_cost, 8),
                'duration_cost': round(duration_cost, 8)
            },
            'monthly_cost_estimates': monthly_estimates
        }
    
    def _find_optimal_configuration(self, scenario_results: List[Dict]) -> Dict:
        """Find optimal configuration balancing cost and performance"""
        
        # Calculate cost-performance score for each configuration
        scored_results = []
        
        for result in scenario_results:
            if 'error' in result['cost_analysis']:
                continue
                
            memory_size = result['memory_size']
            avg_duration = result['performance']['duration_stats']['mean']
            cost_per_invocation = result['cost_analysis']['cost_per_invocation']
            
            # Normalize metrics (lower is better for both duration and cost)
            # Score = weighted combination of normalized cost and duration
            duration_weight = 0.3
            cost_weight = 0.7
            
            # Simple scoring (could be more sophisticated)
            score = (duration_weight * avg_duration) + (cost_weight * cost_per_invocation * 1000000)
            
            scored_results.append({
                'memory_size': memory_size,
                'score': score,
                'avg_duration': avg_duration,
                'cost_per_invocation': cost_per_invocation,
                'result': result
            })
        
        # Find configuration with lowest score (best cost-performance balance)
        if scored_results:
            optimal = min(scored_results, key=lambda x: x['score'])
            return {
                'optimal_memory_size': optimal['memory_size'],
                'avg_duration_ms': optimal['avg_duration'],
                'cost_per_invocation': optimal['cost_per_invocation'],
                'performance_score': optimal['score'],
                'full_result': optimal['result']
            }
        
        return {'error': 'No valid configurations found'}
    
    def _generate_optimization_recommendations(self, results: Dict) -> List[Dict]:
        """Generate optimization recommendations based on all test scenarios"""
        recommendations = []
        
        # Analyze optimal configurations across scenarios
        optimal_memories = []
        for scenario_name, optimal in results['optimal_configurations'].items():
            if 'optimal_memory_size' in optimal:
                optimal_memories.append(optimal['optimal_memory_size'])
        
        if optimal_memories:
            # Find most common optimal memory size
            from collections import Counter
            memory_counts = Counter(optimal_memories)
            most_common_memory = memory_counts.most_common(1)[0][0]
            
            recommendations.append({
                'type': 'memory_optimization',
                'recommendation': f'Set memory to {most_common_memory}MB',
                'reason': f'Optimal for {memory_counts[most_common_memory]}/{len(optimal_memories)} test scenarios',
                'priority': 'high'
            })
            
            # Calculate potential savings
            original_memory = results['original_memory']
            if most_common_memory != original_memory:
                recommendations.append({
                    'type': 'cost_savings',
                    'recommendation': f'Change from {original_memory}MB to {most_common_memory}MB',
                    'reason': 'Optimized memory allocation for cost-performance balance',
                    'priority': 'high'
                })
        
        return recommendations
```

### Lambda Cold Start Optimization
```python
# Optimized Lambda function structure for cold start reduction
import json
import boto3
import os
from typing import Dict, Any

# Initialize clients outside handler (connection reuse)
dynamodb = boto3.resource('dynamodb')
s3_client = boto3.client('s3')

# Cache table reference
table = dynamodb.Table(os.environ['TABLE_NAME'])

# Global variables for caching
cache = {}

def lambda_handler(event: Dict[str, Any], context) -> Dict[str, Any]:
    """Optimized Lambda handler with cold start mitigation"""
    
    # Warm-up check
    if event.get('source') == 'aws.events' and event.get('detail-type') == 'Scheduled Event':
        return {'statusCode': 200, 'body': 'Warm-up successful'}
    
    try:
        # Use cached data if available
        cache_key = event.get('cache_key')
        if cache_key and cache_key in cache:
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'result': cache[cache_key],
                    'cached': True
                })
            }
        
        # Process request
        result = process_request(event)
        
        # Cache result if applicable
        if cache_key:
            cache[cache_key] = result
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'result': result,
                'cached': False
            })
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e)
            })
        }

def process_request(event: Dict[str, Any]) -> Any:
    """Process the actual request"""
    # Your business logic here
    return {'processed': True, 'data': event}

# Provisioned concurrency configuration (Terraform)
```

```hcl
# Provisioned concurrency for critical functions
resource "aws_lambda_provisioned_concurrency_config" "critical_function" {
  function_name                     = aws_lambda_function.critical.function_name
  provisioned_concurrent_executions = 10
  qualifier                        = aws_lambda_function.critical.version
}

# EventBridge rule for warming up functions
resource "aws_cloudwatch_event_rule" "lambda_warmer" {
  name                = "lambda-warmer"
  description         = "Warm up Lambda functions"
  schedule_expression = "rate(5 minutes)"
}

resource "aws_cloudwatch_event_target" "lambda_warmer_target" {
  rule      = aws_cloudwatch_event_rule.lambda_warmer.name
  target_id = "LambdaWarmerTarget"
  arn       = aws_lambda_function.critical.arn
  
  input = jsonencode({
    source      = "aws.events"
    detail-type = "Scheduled Event"
    detail      = {}
  })
}
```

## 🔄 Step Functions Optimization

### Cost-Optimized Step Functions
```json
{
  "Comment": "Cost-optimized Step Functions workflow",
  "StartAt": "CheckCache",
  "States": {
    "CheckCache": {
      "Type": "Task",
      "Resource": "arn:aws:states:::dynamodb:getItem",
      "Parameters": {
        "TableName": "ProcessingCache",
        "Key": {
          "id": {
            "S.$": "$.id"
          }
        }
      },
      "ResultPath": "$.cacheResult",
      "Next": "CacheExists?",
      "Catch": [
        {
          "ErrorEquals": ["States.ALL"],
          "Next": "ProcessData",
          "ResultPath": "$.error"
        }
      ]
    },
    "CacheExists?": {
      "Type": "Choice",
      "Choices": [
        {
          "Variable": "$.cacheResult.Item",
          "IsPresent": true,
          "Next": "ReturnCachedResult"
        }
      ],
      "Default": "ProcessData"
    },
    "ReturnCachedResult": {
      "Type": "Pass",
      "Result": "Returned cached result",
      "End": true
    },
    "ProcessData": {
      "Type": "Parallel",
      "Branches": [
        {
          "StartAt": "ProcessBatch1",
          "States": {
            "ProcessBatch1": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "ProcessingFunction",
                "Payload": {
                  "batch.$": "$.batch1"
                }
              },
              "End": true
            }
          }
        },
        {
          "StartAt": "ProcessBatch2",
          "States": {
            "ProcessBatch2": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "ProcessingFunction",
                "Payload": {
                  "batch.$": "$.batch2"
                }
              },
              "End": true
            }
          }
        }
      ],
      "Next": "CombineResults",
      "Catch": [
        {
          "ErrorEquals": ["States.ALL"],
          "Next": "HandleError"
        }
      ]
    },
    "CombineResults": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "CombineResultsFunction",
        "Payload.$": "$"
      },
      "Next": "SaveToCache"
    },
    "SaveToCache": {
      "Type": "Task",
      "Resource": "arn:aws:states:::dynamodb:putItem",
      "Parameters": {
        "TableName": "ProcessingCache",
        "Item": {
          "id": {
            "S.$": "$.id"
          },
          "result": {
            "S.$": "$.result"
          },
          "ttl": {
            "N.$": "$.ttl"
          }
        }
      },
      "End": true
    },
    "HandleError": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "ErrorHandlerFunction",
        "Payload.$": "$"
      },
      "End": true
    }
  }
}
```

## 🌐 API Gateway Cost Controls

### API Gateway Optimization Strategies
```hcl
# Cost-optimized API Gateway configuration
resource "aws_api_gateway_rest_api" "cost_optimized" {
  name        = "cost-optimized-api"
  description = "Cost-optimized API Gateway"
  
  endpoint_configuration {
    types = ["REGIONAL"]  # Regional endpoints are cheaper than edge-optimized
  }
}

# Enable caching to reduce backend calls
resource "aws_api_gateway_method_settings" "caching" {
  rest_api_id = aws_api_gateway_rest_api.cost_optimized.id
  stage_name  = aws_api_gateway_stage.prod.stage_name
  method_path = "*/*"
  
  settings {
    caching_enabled      = true
    cache_ttl_in_seconds = 300  # 5 minutes
    cache_key_parameters = ["method.request.querystring.id"]
    
    # Enable compression
    data_trace_enabled = false
    logging_level      = "ERROR"  # Reduce CloudWatch costs
    
    # Throttling to control costs
    throttling_rate_limit  = 1000
    throttling_burst_limit = 2000
  }
}

# Usage plan for API key management and throttling
resource "aws_api_gateway_usage_plan" "cost_control" {
  name = "cost-control-plan"
  
  api_stages {
    api_id = aws_api_gateway_rest_api.cost_optimized.id
    stage  = aws_api_gateway_stage.prod.stage_name
  }
  
  quota_settings {
    limit  = 1000000  # 1M requests per month
    period = "MONTH"
  }
  
  throttle_settings {
    rate_limit  = 100
    burst_limit = 200
  }
}
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Do you understand Lambda pricing and optimization strategies?
- [ ] Can you implement Step Functions cost optimization?
- [ ] Do you know how to optimize API Gateway costs?
- [ ] Can you design cost-effective serverless architectures?

### Practical Skills
- [ ] Can you perform Lambda power tuning effectively?
- [ ] Do you know how to reduce cold start impacts?
- [ ] Can you implement caching strategies for serverless?
- [ ] Do you understand serverless monitoring and optimization?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete Lambda power tuning implementations
- Step Functions optimization patterns
- API Gateway cost control configurations
- Serverless monitoring setups

## ➡️ Next Steps
Continue to [Module 13: Cost Governance and Policy Enforcement](../13_cost_governance_and_policy_enforcement/)

---
**Module Duration**: 1 week  
**Difficulty**: Advanced  
**Prerequisites**: Module 11 completion
