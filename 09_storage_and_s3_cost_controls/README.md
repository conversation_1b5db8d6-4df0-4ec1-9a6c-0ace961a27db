# 📘 Module 09: Storage and S3 Cost Controls

## 🎯 Learning Goals
By the end of this module, you will:
- Master S3 storage classes and intelligent tiering strategies
- Implement automated lifecycle policies for cost optimization
- Optimize EBS volumes and snapshot management
- Set up comprehensive storage monitoring and alerting
- Leverage both free and premium tools for storage optimization

## 📚 Table of Contents
1. [S3 Storage Classes and Optimization](#s3-storage-classes-and-optimization)
2. [Lifecycle Policies and Automation](#lifecycle-policies-and-automation)
3. [EBS Volume Optimization](#ebs-volume-optimization)
4. [Storage Monitoring and Analytics](#storage-monitoring-and-analytics)
5. [Tools and Integration](#tools-and-integration)
6. [Study Checkpoints](#study-checkpoints)
7. [Practical Examples](#practical-examples)

## 🗄️ S3 Storage Classes and Optimization

### S3 Storage Classes Overview
```yaml
s3_storage_classes:
  standard:
    use_case: "Frequently accessed data"
    availability: "99.999999999% (11 9's)"
    cost_per_gb: "$0.023/GB/month"
    retrieval_cost: "None"
    minimum_duration: "None"
    
  standard_ia:
    use_case: "Infrequently accessed data"
    availability: "99.999999999% (11 9's)"
    cost_per_gb: "$0.0125/GB/month"
    retrieval_cost: "$0.01/GB"
    minimum_duration: "30 days"
    
  one_zone_ia:
    use_case: "Infrequent access, single AZ"
    availability: "99.999999999% (11 9's)"
    cost_per_gb: "$0.01/GB/month"
    retrieval_cost: "$0.01/GB"
    minimum_duration: "30 days"
    
  glacier_instant_retrieval:
    use_case: "Archive with instant access"
    availability: "99.999999999% (11 9's)"
    cost_per_gb: "$0.004/GB/month"
    retrieval_cost: "$0.03/GB"
    minimum_duration: "90 days"
    
  glacier_flexible_retrieval:
    use_case: "Archive with 1-12 hour retrieval"
    availability: "99.999999999% (11 9's)"
    cost_per_gb: "$0.0036/GB/month"
    retrieval_cost: "Variable by speed"
    minimum_duration: "90 days"
    
  glacier_deep_archive:
    use_case: "Long-term archive, 12+ hour retrieval"
    availability: "99.999999999% (11 9's)"
    cost_per_gb: "$0.00099/GB/month"
    retrieval_cost: "$0.02/GB"
    minimum_duration: "180 days"
    
  intelligent_tiering:
    use_case: "Unknown or changing access patterns"
    availability: "99.999999999% (11 9's)"
    cost_per_gb: "Variable + $0.0025/1000 objects"
    retrieval_cost: "None for frequent/infrequent"
    minimum_duration: "None"
```

### S3 Cost Optimization Analysis
```python
import boto3
import json
from datetime import datetime, timedelta
from typing import Dict, List

class S3CostOptimizer:
    def __init__(self):
        self.s3 = boto3.client('s3')
        self.cloudwatch = boto3.client('cloudwatch')
        self.ce = boto3.client('ce')
        
    def analyze_bucket_usage(self, bucket_name: str, days: int = 30) -> Dict:
        """Analyze S3 bucket usage patterns for optimization"""
        
        # Get bucket size and object count
        try:
            size_response = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/S3',
                MetricName='BucketSizeBytes',
                Dimensions=[
                    {'Name': 'BucketName', 'Value': bucket_name},
                    {'Name': 'StorageType', 'Value': 'StandardStorage'}
                ],
                StartTime=datetime.utcnow() - timedelta(days=days),
                EndTime=datetime.utcnow(),
                Period=86400,  # Daily
                Statistics=['Average']
            )
            
            object_count_response = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/S3',
                MetricName='NumberOfObjects',
                Dimensions=[
                    {'Name': 'BucketName', 'Value': bucket_name},
                    {'Name': 'StorageType', 'Value': 'AllStorageTypes'}
                ],
                StartTime=datetime.utcnow() - timedelta(days=days),
                EndTime=datetime.utcnow(),
                Period=86400,
                Statistics=['Average']
            )
            
            # Calculate average size and object count
            avg_size_bytes = 0
            if size_response['Datapoints']:
                avg_size_bytes = sum(dp['Average'] for dp in size_response['Datapoints']) / len(size_response['Datapoints'])
            
            avg_object_count = 0
            if object_count_response['Datapoints']:
                avg_object_count = sum(dp['Average'] for dp in object_count_response['Datapoints']) / len(object_count_response['Datapoints'])
            
            # Get access patterns (simplified - would need CloudTrail for real analysis)
            access_analysis = self._analyze_access_patterns(bucket_name, days)
            
            # Generate recommendations
            recommendations = self._generate_storage_recommendations(
                bucket_name, avg_size_bytes, avg_object_count, access_analysis
            )
            
            return {
                'bucket_name': bucket_name,
                'average_size_gb': round(avg_size_bytes / (1024**3), 2),
                'average_object_count': round(avg_object_count, 0),
                'access_analysis': access_analysis,
                'recommendations': recommendations,
                'potential_savings': self._calculate_potential_savings(avg_size_bytes, recommendations)
            }
            
        except Exception as e:
            return {'error': f'Error analyzing bucket {bucket_name}: {str(e)}'}
    
    def _analyze_access_patterns(self, bucket_name: str, days: int) -> Dict:
        """Analyze object access patterns (simplified version)"""
        try:
            # Get download/request metrics
            get_requests = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/S3',
                MetricName='AllRequests',
                Dimensions=[
                    {'Name': 'BucketName', 'Value': bucket_name},
                    {'Name': 'FilterId', 'Value': 'EntireBucket'}
                ],
                StartTime=datetime.utcnow() - timedelta(days=days),
                EndTime=datetime.utcnow(),
                Period=86400,
                Statistics=['Sum']
            )
            
            total_requests = sum(dp['Sum'] for dp in get_requests['Datapoints'])
            daily_avg_requests = total_requests / days if days > 0 else 0
            
            # Classify access pattern
            if daily_avg_requests > 100:
                access_pattern = "frequent"
            elif daily_avg_requests > 10:
                access_pattern = "infrequent"
            else:
                access_pattern = "archive"
            
            return {
                'total_requests': total_requests,
                'daily_average_requests': round(daily_avg_requests, 2),
                'access_pattern': access_pattern
            }
            
        except Exception as e:
            return {'error': f'Error analyzing access patterns: {str(e)}'}
    
    def _generate_storage_recommendations(self, bucket_name: str, size_bytes: float, 
                                        object_count: float, access_analysis: Dict) -> List[Dict]:
        """Generate storage class recommendations"""
        recommendations = []
        
        access_pattern = access_analysis.get('access_pattern', 'unknown')
        size_gb = size_bytes / (1024**3)
        
        if access_pattern == "frequent":
            if size_gb > 1000:  # Large buckets
                recommendations.append({
                    'action': 'Enable S3 Intelligent Tiering',
                    'reason': 'Large bucket with frequent access - let AWS optimize automatically',
                    'estimated_savings_percentage': 15
                })
            else:
                recommendations.append({
                    'action': 'Keep in S3 Standard',
                    'reason': 'Frequent access pattern suits S3 Standard',
                    'estimated_savings_percentage': 0
                })
                
        elif access_pattern == "infrequent":
            recommendations.append({
                'action': 'Transition to S3 Standard-IA after 30 days',
                'reason': 'Infrequent access pattern suits Standard-IA',
                'estimated_savings_percentage': 45
            })
            
            if size_gb > 500:
                recommendations.append({
                    'action': 'Enable S3 Intelligent Tiering',
                    'reason': 'Large bucket - intelligent tiering may provide additional savings',
                    'estimated_savings_percentage': 25
                })
                
        elif access_pattern == "archive":
            recommendations.append({
                'action': 'Transition to Glacier Flexible Retrieval after 90 days',
                'reason': 'Archive access pattern suits Glacier',
                'estimated_savings_percentage': 85
            })
            
            recommendations.append({
                'action': 'Consider Glacier Deep Archive for long-term retention',
                'reason': 'Minimal access - Deep Archive provides maximum savings',
                'estimated_savings_percentage': 95
            })
        
        return recommendations
    
    def _calculate_potential_savings(self, size_bytes: float, recommendations: List[Dict]) -> Dict:
        """Calculate potential cost savings"""
        size_gb = size_bytes / (1024**3)
        
        # Current cost (assuming S3 Standard)
        current_monthly_cost = size_gb * 0.023  # $0.023/GB/month for Standard
        
        # Calculate savings for best recommendation
        best_savings_percentage = 0
        if recommendations:
            best_savings_percentage = max(rec.get('estimated_savings_percentage', 0) for rec in recommendations)
        
        monthly_savings = current_monthly_cost * (best_savings_percentage / 100)
        annual_savings = monthly_savings * 12
        
        return {
            'current_monthly_cost': round(current_monthly_cost, 2),
            'potential_monthly_savings': round(monthly_savings, 2),
            'potential_annual_savings': round(annual_savings, 2),
            'savings_percentage': best_savings_percentage
        }
```

### Intelligent Tiering Configuration
```python
def setup_intelligent_tiering(bucket_name: str, prefix: str = "") -> Dict:
    """Set up S3 Intelligent Tiering for a bucket"""
    s3 = boto3.client('s3')
    
    try:
        # Create Intelligent Tiering configuration
        configuration = {
            'Id': 'intelligent-tiering-config',
            'Status': 'Enabled',
            'Filter': {
                'Prefix': prefix
            },
            'Tierings': [
                {
                    'Days': 1,
                    'AccessTier': 'ARCHIVE_ACCESS'
                },
                {
                    'Days': 90,
                    'AccessTier': 'DEEP_ARCHIVE_ACCESS'
                }
            ],
            'OptionalFields': ['BucketKeyStatus']
        }
        
        s3.put_bucket_intelligent_tiering_configuration(
            Bucket=bucket_name,
            Id='intelligent-tiering-config',
            IntelligentTieringConfiguration=configuration
        )
        
        return {
            'success': True,
            'message': f'Intelligent Tiering enabled for bucket {bucket_name}',
            'configuration': configuration
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'Error setting up Intelligent Tiering: {str(e)}'
        }
```

## 🔄 Lifecycle Policies and Automation

### Comprehensive Lifecycle Policy
```json
{
  "Rules": [
    {
      "ID": "OptimizeStorageCosts",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "logs/"
      },
      "Transitions": [
        {
          "Days": 30,
          "StorageClass": "STANDARD_IA"
        },
        {
          "Days": 90,
          "StorageClass": "GLACIER"
        },
        {
          "Days": 365,
          "StorageClass": "DEEP_ARCHIVE"
        }
      ],
      "Expiration": {
        "Days": 2555
      },
      "NoncurrentVersionTransitions": [
        {
          "NoncurrentDays": 30,
          "StorageClass": "STANDARD_IA"
        },
        {
          "NoncurrentDays": 90,
          "StorageClass": "GLACIER"
        }
      ],
      "NoncurrentVersionExpiration": {
        "NoncurrentDays": 365
      },
      "AbortIncompleteMultipartUpload": {
        "DaysAfterInitiation": 7
      }
    },
    {
      "ID": "DeleteIncompleteUploads",
      "Status": "Enabled",
      "Filter": {},
      "AbortIncompleteMultipartUpload": {
        "DaysAfterInitiation": 1
      }
    },
    {
      "ID": "IntelligentTieringForLargeObjects",
      "Status": "Enabled",
      "Filter": {
        "ObjectSizeGreaterThan": 128000
      },
      "Transitions": [
        {
          "Days": 0,
          "StorageClass": "INTELLIGENT_TIERING"
        }
      ]
    }
  ]
}
```

### Terraform Lifecycle Policy Implementation
```hcl
# S3 bucket with comprehensive lifecycle policy
resource "aws_s3_bucket" "optimized_storage" {
  bucket = "company-optimized-storage-${random_string.bucket_suffix.result}"
}

resource "aws_s3_bucket_lifecycle_configuration" "storage_optimization" {
  bucket = aws_s3_bucket.optimized_storage.id

  rule {
    id     = "optimize_storage_costs"
    status = "Enabled"

    filter {
      prefix = "logs/"
    }

    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }

    transition {
      days          = 90
      storage_class = "GLACIER"
    }

    transition {
      days          = 365
      storage_class = "DEEP_ARCHIVE"
    }

    expiration {
      days = 2555  # 7 years
    }

    noncurrent_version_transition {
      noncurrent_days = 30
      storage_class   = "STANDARD_IA"
    }

    noncurrent_version_transition {
      noncurrent_days = 90
      storage_class   = "GLACIER"
    }

    noncurrent_version_expiration {
      noncurrent_days = 365
    }

    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }
  }

  rule {
    id     = "intelligent_tiering_large_objects"
    status = "Enabled"

    filter {
      object_size_greater_than = 128000  # 128KB
    }

    transition {
      days          = 0
      storage_class = "INTELLIGENT_TIERING"
    }
  }

  rule {
    id     = "delete_incomplete_uploads"
    status = "Enabled"

    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }
  }
}

# Intelligent Tiering configuration
resource "aws_s3_bucket_intelligent_tiering_configuration" "entire_bucket" {
  bucket = aws_s3_bucket.optimized_storage.id
  name   = "entire-bucket-intelligent-tiering"

  status = "Enabled"

  tiering {
    access_tier = "ARCHIVE_ACCESS"
    days        = 90
  }

  tiering {
    access_tier = "DEEP_ARCHIVE_ACCESS"
    days        = 180
  }

  optional_fields = ["BucketKeyStatus"]
}
```

## 💾 EBS Volume Optimization

### EBS Volume Analysis and Optimization
```python
import boto3
from datetime import datetime, timedelta
from typing import Dict, List

class EBSOptimizer:
    def __init__(self):
        self.ec2 = boto3.client('ec2')
        self.cloudwatch = boto3.client('cloudwatch')
        
    def analyze_ebs_volumes(self, region: str = None) -> List[Dict]:
        """Analyze EBS volumes for optimization opportunities"""
        volumes = self.ec2.describe_volumes()
        optimization_opportunities = []
        
        for volume in volumes['Volumes']:
            volume_id = volume['VolumeId']
            volume_type = volume['VolumeType']
            size = volume['Size']
            state = volume['State']
            
            # Get volume metrics
            metrics = self._get_volume_metrics(volume_id)
            
            # Analyze optimization opportunities
            recommendations = self._analyze_volume_performance(volume, metrics)
            
            if recommendations:
                optimization_opportunities.append({
                    'volume_id': volume_id,
                    'current_type': volume_type,
                    'size_gb': size,
                    'state': state,
                    'metrics': metrics,
                    'recommendations': recommendations,
                    'potential_savings': self._calculate_ebs_savings(volume, recommendations)
                })
        
        return optimization_opportunities
    
    def _get_volume_metrics(self, volume_id: str, days: int = 7) -> Dict:
        """Get CloudWatch metrics for EBS volume"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)
        
        metrics = {}
        
        # Get IOPS metrics
        try:
            read_ops = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/EBS',
                MetricName='VolumeReadOps',
                Dimensions=[{'Name': 'VolumeId', 'Value': volume_id}],
                StartTime=start_time,
                EndTime=end_time,
                Period=3600,
                Statistics=['Sum']
            )
            
            write_ops = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/EBS',
                MetricName='VolumeWriteOps',
                Dimensions=[{'Name': 'VolumeId', 'Value': volume_id}],
                StartTime=start_time,
                EndTime=end_time,
                Period=3600,
                Statistics=['Sum']
            )
            
            # Calculate average IOPS
            total_read_ops = sum(dp['Sum'] for dp in read_ops['Datapoints'])
            total_write_ops = sum(dp['Sum'] for dp in write_ops['Datapoints'])
            hours = len(read_ops['Datapoints'])
            
            avg_read_iops = total_read_ops / hours if hours > 0 else 0
            avg_write_iops = total_write_ops / hours if hours > 0 else 0
            
            metrics = {
                'avg_read_iops': round(avg_read_iops, 2),
                'avg_write_iops': round(avg_write_iops, 2),
                'total_iops': round(avg_read_iops + avg_write_iops, 2)
            }
            
        except Exception as e:
            metrics = {'error': f'Error getting metrics: {str(e)}'}
        
        return metrics
    
    def _analyze_volume_performance(self, volume: Dict, metrics: Dict) -> List[Dict]:
        """Analyze volume and recommend optimizations"""
        recommendations = []
        
        volume_type = volume['VolumeType']
        size = volume['Size']
        iops = volume.get('Iops', 0)
        total_iops = metrics.get('total_iops', 0)
        
        # Check if volume is unattached
        if volume['State'] == 'available':
            recommendations.append({
                'type': 'delete_unattached',
                'description': 'Volume is unattached and can be deleted',
                'priority': 'high',
                'savings_type': 'cost_elimination'
            })
            return recommendations
        
        # Analyze volume type optimization
        if volume_type == 'gp2' and size >= 334:  # gp2 volumes >= 334GB get 3 IOPS/GB
            if total_iops < size * 3 * 0.1:  # Using less than 10% of available IOPS
                recommendations.append({
                    'type': 'downgrade_to_gp3',
                    'description': 'Low IOPS usage - gp3 would be more cost effective',
                    'recommended_type': 'gp3',
                    'priority': 'medium',
                    'savings_type': 'cost_reduction'
                })
        
        elif volume_type == 'io1' or volume_type == 'io2':
            if total_iops < iops * 0.2:  # Using less than 20% of provisioned IOPS
                recommendations.append({
                    'type': 'reduce_provisioned_iops',
                    'description': 'Provisioned IOPS underutilized',
                    'recommended_iops': max(100, int(total_iops * 1.5)),
                    'priority': 'medium',
                    'savings_type': 'cost_reduction'
                })
        
        # Check for oversized volumes (simplified check)
        if total_iops < 10 and size > 100:  # Very low usage on large volume
            recommendations.append({
                'type': 'consider_resizing',
                'description': 'Volume appears oversized for current usage',
                'priority': 'low',
                'savings_type': 'cost_reduction'
            })
        
        return recommendations
    
    def _calculate_ebs_savings(self, volume: Dict, recommendations: List[Dict]) -> Dict:
        """Calculate potential savings from EBS optimizations"""
        volume_type = volume['VolumeType']
        size = volume['Size']
        iops = volume.get('Iops', 0)
        
        # Simplified pricing (varies by region)
        pricing = {
            'gp2': 0.10,  # per GB/month
            'gp3': 0.08,  # per GB/month
            'io1': 0.125, # per GB/month + $0.065 per IOPS/month
            'io2': 0.125, # per GB/month + $0.065 per IOPS/month
        }
        
        current_cost = 0
        if volume_type in pricing:
            current_cost = size * pricing[volume_type]
            if volume_type in ['io1', 'io2']:
                current_cost += iops * 0.065
        
        # Calculate savings for each recommendation
        max_savings = 0
        for rec in recommendations:
            if rec['type'] == 'delete_unattached':
                max_savings = current_cost  # 100% savings
            elif rec['type'] == 'downgrade_to_gp3':
                new_cost = size * pricing['gp3']
                max_savings = max(max_savings, current_cost - new_cost)
            elif rec['type'] == 'reduce_provisioned_iops':
                new_iops = rec.get('recommended_iops', iops)
                new_cost = size * pricing[volume_type] + new_iops * 0.065
                max_savings = max(max_savings, current_cost - new_cost)
        
        return {
            'current_monthly_cost': round(current_cost, 2),
            'potential_monthly_savings': round(max_savings, 2),
            'potential_annual_savings': round(max_savings * 12, 2),
            'savings_percentage': round((max_savings / current_cost * 100), 1) if current_cost > 0 else 0
        }
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Do you understand S3 storage classes and their use cases?
- [ ] Can you design effective lifecycle policies?
- [ ] Do you know how to optimize EBS volumes?
- [ ] Can you implement storage monitoring and alerting?

### Practical Skills
- [ ] Can you analyze S3 usage patterns and costs?
- [ ] Do you know how to set up Intelligent Tiering?
- [ ] Can you identify EBS optimization opportunities?
- [ ] Do you understand when to use premium storage tools?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete S3 cost analysis scripts
- Lifecycle policy templates
- EBS optimization tools
- Storage monitoring configurations

## ➡️ Next Steps
Continue to [Module 10: Kubernetes Cost Optimization](../10_kubernetes_cost_optimization/)

---
**Module Duration**: 1 week  
**Difficulty**: Intermediate  
**Prerequisites**: Module 08 completion
