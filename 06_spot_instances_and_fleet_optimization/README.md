# 📘 Module 06: Spot Instances and Fleet Optimization

## 🎯 Learning Goals
By the end of this module, you will:
- Master EC2 Spot Instance lifecycle and best practices
- Design fault-tolerant applications using Spot Instances
- Implement fleet configurations with fallback strategies
- Optimize Kubernetes workloads with Spot Instances
- Leverage both free and premium tools for Spot optimization

## 📚 Table of Contents
1. [EC2 Spot Instance Fundamentals](#ec2-spot-instance-fundamentals)
2. [Fleet Configuration Strategies](#fleet-configuration-strategies)
3. [Kubernetes and Spot Integration](#kubernetes-and-spot-integration)
4. [Monitoring and Optimization](#monitoring-and-optimization)
5. [Tools and Platforms](#tools-and-platforms)
6. [Study Checkpoints](#study-checkpoints)
7. [Practical Examples](#practical-examples)

## ⚡ EC2 Spot Instance Fundamentals

### Spot Instance Lifecycle
```mermaid
graph TD
    A[Spot Request] --> B[Pending]
    B --> C[Active]
    C --> D[Running]
    D --> E[Spot Interruption]
    E --> F[Terminating]
    F --> G[Terminated]
    
    D --> H[Manual Termination]
    H --> F
    
    B --> I[Request Failed]
    I --> J[Cancelled]
```

### Key Concepts
```yaml
spot_fundamentals:
  pricing_model:
    description: "Pay market price, up to 90% savings"
    price_determination: "Supply and demand"
    price_changes: "Gradual, not sudden spikes"
    
  interruption_notice:
    warning_time: "2 minutes"
    notification_method: "Instance metadata + CloudWatch Events"
    actions_available: ["Stop", "Terminate", "Hibernate"]
    
  capacity_types:
    on_demand: "Guaranteed capacity, highest cost"
    spot: "Interruptible capacity, lowest cost"
    reserved: "Committed capacity, medium cost"
    
  best_practices:
    - "Design for interruption tolerance"
    - "Use multiple instance types and AZs"
    - "Implement graceful shutdown handling"
    - "Monitor Spot price trends"
```

### Spot Instance Best Practices
```python
import boto3
import json
from datetime import datetime, timedelta

class SpotInstanceManager:
    def __init__(self):
        self.ec2 = boto3.client('ec2')
        self.cloudwatch = boto3.client('cloudwatch')
        
    def get_spot_price_history(self, instance_types: list, days: int = 7):
        """Get Spot price history for analysis"""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)
        
        price_history = {}
        
        for instance_type in instance_types:
            response = self.ec2.describe_spot_price_history(
                InstanceTypes=[instance_type],
                ProductDescriptions=['Linux/UNIX'],
                StartTime=start_time,
                EndTime=end_time
            )
            
            prices = []
            for price_point in response['SpotPriceHistory']:
                prices.append({
                    'timestamp': price_point['Timestamp'].isoformat(),
                    'price': float(price_point['SpotPrice']),
                    'availability_zone': price_point['AvailabilityZone']
                })
            
            price_history[instance_type] = prices
            
        return price_history
    
    def calculate_savings_potential(self, instance_type: str, hours_per_month: int = 730):
        """Calculate potential savings using Spot vs On-Demand"""
        # Get current On-Demand pricing (simplified)
        on_demand_prices = {
            't3.micro': 0.0104, 't3.small': 0.0208, 't3.medium': 0.0416,
            't3.large': 0.0832, 't3.xlarge': 0.1664, 't3.2xlarge': 0.3328,
            'm5.large': 0.096, 'm5.xlarge': 0.192, 'm5.2xlarge': 0.384,
            'c5.large': 0.085, 'c5.xlarge': 0.17, 'c5.2xlarge': 0.34
        }
        
        # Get recent Spot price
        spot_prices = self.ec2.describe_spot_price_history(
            InstanceTypes=[instance_type],
            ProductDescriptions=['Linux/UNIX'],
            MaxResults=1
        )
        
        if not spot_prices['SpotPriceHistory']:
            return None
            
        current_spot_price = float(spot_prices['SpotPriceHistory'][0]['SpotPrice'])
        on_demand_price = on_demand_prices.get(instance_type, 0)
        
        if on_demand_price == 0:
            return None
            
        monthly_on_demand_cost = on_demand_price * hours_per_month
        monthly_spot_cost = current_spot_price * hours_per_month
        monthly_savings = monthly_on_demand_cost - monthly_spot_cost
        savings_percentage = (monthly_savings / monthly_on_demand_cost) * 100
        
        return {
            'instance_type': instance_type,
            'on_demand_hourly': on_demand_price,
            'spot_hourly': current_spot_price,
            'monthly_on_demand_cost': round(monthly_on_demand_cost, 2),
            'monthly_spot_cost': round(monthly_spot_cost, 2),
            'monthly_savings': round(monthly_savings, 2),
            'savings_percentage': round(savings_percentage, 1)
        }
```

### Spot Interruption Handling
```python
import requests
import signal
import sys
import time
import logging

class SpotInterruptionHandler:
    def __init__(self):
        self.metadata_url = "http://169.254.169.254/latest/meta-data/spot/instance-action"
        self.shutdown_initiated = False
        
        # Set up signal handlers
        signal.signal(signal.SIGTERM, self.handle_termination)
        signal.signal(signal.SIGINT, self.handle_termination)
        
    def check_interruption_notice(self):
        """Check for Spot interruption notice"""
        try:
            response = requests.get(self.metadata_url, timeout=2)
            if response.status_code == 200:
                interruption_data = response.json()
                return interruption_data
        except requests.RequestException:
            # No interruption notice (404 is expected when no interruption)
            pass
        return None
    
    def handle_termination(self, signum, frame):
        """Handle termination signals gracefully"""
        if self.shutdown_initiated:
            return
            
        self.shutdown_initiated = True
        logging.info(f"Received signal {signum}, initiating graceful shutdown...")
        
        # Perform cleanup tasks
        self.graceful_shutdown()
        sys.exit(0)
    
    def graceful_shutdown(self):
        """Perform graceful shutdown tasks"""
        logging.info("Starting graceful shutdown process...")
        
        # 1. Stop accepting new work
        self.stop_accepting_work()
        
        # 2. Complete current work
        self.complete_current_work()
        
        # 3. Save state/data
        self.save_application_state()
        
        # 4. Cleanup resources
        self.cleanup_resources()
        
        logging.info("Graceful shutdown completed")
    
    def stop_accepting_work(self):
        """Stop accepting new work/requests"""
        # Implementation depends on your application
        # Examples: Remove from load balancer, stop queue polling
        pass
    
    def complete_current_work(self):
        """Wait for current work to complete"""
        # Implementation depends on your application
        # Examples: Wait for current requests to finish
        pass
    
    def save_application_state(self):
        """Save application state for recovery"""
        # Implementation depends on your application
        # Examples: Save to S3, database, etc.
        pass
    
    def cleanup_resources(self):
        """Cleanup resources before termination"""
        # Implementation depends on your application
        # Examples: Close connections, release locks
        pass
    
    def monitor_interruption(self, check_interval: int = 5):
        """Continuously monitor for interruption notices"""
        while not self.shutdown_initiated:
            interruption_notice = self.check_interruption_notice()
            
            if interruption_notice:
                logging.warning(f"Spot interruption notice received: {interruption_notice}")
                self.graceful_shutdown()
                break
                
            time.sleep(check_interval)

# Usage in your application
if __name__ == "__main__":
    handler = SpotInterruptionHandler()
    
    # Start monitoring in a separate thread
    import threading
    monitor_thread = threading.Thread(target=handler.monitor_interruption)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    # Your application logic here
    while True:
        # Your application work
        time.sleep(1)
```

## 🚀 Fleet Configuration Strategies

### Mixed Instance Types Strategy
```yaml
fleet_configuration:
  diversification_strategy:
    instance_families: ["m5", "m5a", "m5n", "m4"]
    instance_sizes: ["large", "xlarge", "2xlarge"]
    availability_zones: ["us-east-1a", "us-east-1b", "us-east-1c"]
    
  allocation_strategy:
    spot_allocation: "diversified"  # or "lowest-price"
    on_demand_allocation: "prioritized"
    spot_instance_pools: 4  # Minimum number of pools
    
  capacity_distribution:
    spot_percentage: 70
    on_demand_percentage: 30
    spot_max_price: "on_demand_price"  # Don't pay more than On-Demand
```

### Auto Scaling Group with Mixed Instances
```hcl
# Terraform configuration for mixed instance ASG
resource "aws_autoscaling_group" "mixed_instance_asg" {
  name                = "mixed-instance-asg"
  vpc_zone_identifier = var.subnet_ids
  target_group_arns   = [aws_lb_target_group.app.arn]
  health_check_type   = "ELB"
  
  min_size         = 2
  max_size         = 20
  desired_capacity = 6
  
  mixed_instances_policy {
    launch_template {
      launch_template_specification {
        launch_template_id = aws_launch_template.mixed_instance.id
        version           = "$Latest"
      }
      
      override {
        instance_type     = "m5.large"
        weighted_capacity = "1"
      }
      
      override {
        instance_type     = "m5a.large"
        weighted_capacity = "1"
      }
      
      override {
        instance_type     = "m5n.large"
        weighted_capacity = "1"
      }
      
      override {
        instance_type     = "m4.large"
        weighted_capacity = "1"
      }
    }
    
    instances_distribution {
      on_demand_allocation_strategy            = "prioritized"
      on_demand_base_capacity                 = 2
      on_demand_percentage_above_base_capacity = 30
      spot_allocation_strategy                = "capacity-optimized"
      spot_instance_pools                     = 4
      spot_max_price                          = ""  # Use On-Demand price
    }
  }
  
  tag {
    key                 = "Name"
    value               = "mixed-instance-asg"
    propagate_at_launch = true
  }
  
  tag {
    key                 = "Environment"
    value               = "production"
    propagate_at_launch = true
  }
}

# Launch template for mixed instances
resource "aws_launch_template" "mixed_instance" {
  name_prefix   = "mixed-instance-"
  image_id      = data.aws_ami.amazon_linux.id
  instance_type = "m5.large"  # Default, will be overridden
  
  vpc_security_group_ids = [aws_security_group.app.id]
  
  iam_instance_profile {
    name = aws_iam_instance_profile.app.name
  }
  
  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    app_name = "my-app"
  }))
  
  tag_specifications {
    resource_type = "instance"
    tags = {
      Name        = "mixed-instance"
      Environment = "production"
      SpotOptimized = "true"
    }
  }
}
```

### Spot Fleet Configuration
```yaml
spot_fleet_config:
  target_capacity: 10
  allocation_strategy: "diversified"
  
  launch_specifications:
    - image_id: "ami-12345678"
      instance_type: "m5.large"
      key_name: "my-key"
      security_groups: ["sg-12345678"]
      subnet_id: "subnet-12345678"
      weighted_capacity: 1
      
    - image_id: "ami-12345678"
      instance_type: "m5a.large"
      key_name: "my-key"
      security_groups: ["sg-12345678"]
      subnet_id: "subnet-87654321"
      weighted_capacity: 1
      
    - image_id: "ami-12345678"
      instance_type: "c5.large"
      key_name: "my-key"
      security_groups: ["sg-12345678"]
      subnet_id: "subnet-11111111"
      weighted_capacity: 1
  
  spot_price: "0.10"  # Maximum price per hour
  terminate_instances_with_expiration: true
  type: "maintain"  # or "request"
```

## ☸️ Kubernetes and Spot Integration

### Karpenter Configuration
```yaml
# Karpenter NodePool for Spot instances
apiVersion: karpenter.sh/v1beta1
kind: NodePool
metadata:
  name: spot-nodepool
spec:
  template:
    metadata:
      labels:
        node-type: "spot"
    spec:
      requirements:
        - key: kubernetes.io/arch
          operator: In
          values: ["amd64"]
        - key: karpenter.sh/capacity-type
          operator: In
          values: ["spot"]
        - key: node.kubernetes.io/instance-type
          operator: In
          values: ["m5.large", "m5.xlarge", "m5a.large", "m5a.xlarge", "c5.large", "c5.xlarge"]
      
      nodeClassRef:
        apiVersion: karpenter.k8s.aws/v1beta1
        kind: EC2NodeClass
        name: spot-nodeclass
      
      taints:
        - key: spot-instance
          value: "true"
          effect: NoSchedule
  
  limits:
    cpu: 1000
    memory: 1000Gi
  
  disruption:
    consolidationPolicy: WhenEmpty
    consolidateAfter: 30s
    expireAfter: 2160h # 90 days

---
apiVersion: karpenter.k8s.aws/v1beta1
kind: EC2NodeClass
metadata:
  name: spot-nodeclass
spec:
  amiFamily: AL2
  subnetSelectorTerms:
    - tags:
        karpenter.sh/discovery: "my-cluster"
  securityGroupSelectorTerms:
    - tags:
        karpenter.sh/discovery: "my-cluster"
  instanceStorePolicy: RAID0
  userData: |
    #!/bin/bash
    /etc/eks/bootstrap.sh my-cluster
    
    # Install Spot interruption handler
    curl -O https://github.com/aws/aws-node-termination-handler/releases/download/v1.19.0/node-termination-handler.linux.amd64
    chmod +x node-termination-handler.linux.amd64
    mv node-termination-handler.linux.amd64 /usr/local/bin/node-termination-handler
    
    # Create systemd service
    cat > /etc/systemd/system/node-termination-handler.service << EOF
    [Unit]
    Description=AWS Node Termination Handler
    After=network.target
    
    [Service]
    Type=simple
    ExecStart=/usr/local/bin/node-termination-handler --node-name=\$(hostname) --kubernetes-service-host=\$KUBERNETES_SERVICE_HOST --kubernetes-service-port=\$KUBERNETES_SERVICE_PORT
    Restart=always
    
    [Install]
    WantedBy=multi-user.target
    EOF
    
    systemctl enable node-termination-handler
    systemctl start node-termination-handler
```

### Spot-Tolerant Workload Configuration
```yaml
# Deployment that can run on Spot instances
apiVersion: apps/v1
kind: Deployment
metadata:
  name: spot-tolerant-app
spec:
  replicas: 6
  selector:
    matchLabels:
      app: spot-tolerant-app
  template:
    metadata:
      labels:
        app: spot-tolerant-app
    spec:
      # Tolerate Spot instance taints
      tolerations:
        - key: spot-instance
          operator: Equal
          value: "true"
          effect: NoSchedule
      
      # Prefer Spot instances but allow On-Demand as fallback
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              preference:
                matchExpressions:
                  - key: karpenter.sh/capacity-type
                    operator: In
                    values: ["spot"]
            - weight: 50
              preference:
                matchExpressions:
                  - key: karpenter.sh/capacity-type
                    operator: In
                    values: ["on-demand"]
      
      # Anti-affinity to spread across nodes
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values: ["spot-tolerant-app"]
                topologyKey: kubernetes.io/hostname
      
      containers:
        - name: app
          image: nginx:latest
          ports:
            - containerPort: 80
          
          # Graceful shutdown handling
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 15"]
          
          # Resource requests for proper scheduling
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
      
      # Graceful termination
      terminationGracePeriodSeconds: 30
```

### AWS Node Termination Handler
```yaml
# DaemonSet for handling Spot interruptions
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: aws-node-termination-handler
  namespace: kube-system
spec:
  selector:
    matchLabels:
      app: aws-node-termination-handler
  template:
    metadata:
      labels:
        app: aws-node-termination-handler
    spec:
      serviceAccountName: aws-node-termination-handler
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
        - name: aws-node-termination-handler
          image: public.ecr.aws/aws-ec2/aws-node-termination-handler:v1.19.0
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args:
            - --node-name=$(NODE_NAME)
            - --pod-name=$(POD_NAME)
            - --namespace=$(NAMESPACE)
            - --delete-local-data
            - --ignore-daemon-sets
            - --enable-spot-interruption-draining
            - --enable-scheduled-event-draining
          resources:
            requests:
              cpu: 50m
              memory: 64Mi
            limits:
              cpu: 100m
              memory: 128Mi
      tolerations:
        - operator: Exists
```

## 🛠️ Tools and Platforms

### Free Tools

#### AWS EC2 Spot Advisor
- **Interruption frequency** data for instance types
- **Savings potential** analysis
- **Regional availability** information
- **Historical trends** and patterns

#### Karpenter (Open Source)
- **Automatic node provisioning** for Kubernetes
- **Spot instance optimization** with fallback
- **Cost-aware scheduling** decisions
- **Fast node provisioning** and deprovisioning

#### Cluster Autoscaler
- **Traditional Kubernetes** autoscaling
- **Spot instance support** with mixed instance types
- **Node group management** for different capacity types
- **Integration with ASGs**

### Premium Tools

#### Spot.io by NetApp
- **Intelligent Spot management** with ML predictions
- **Automatic fallback** to On-Demand when needed
- **Continuous optimization** of instance mix
- **Enterprise-grade** reliability and support

#### Ocean for EKS
- **Kubernetes-native** Spot optimization
- **Workload-aware** scheduling and scaling
- **Automatic rightsizing** recommendations
- **Cost analytics** and reporting

### Tool Comparison
```yaml
tool_comparison:
  karpenter:
    cost: "Free (open source)"
    complexity: "Medium"
    kubernetes_native: true
    spot_optimization: "Good"
    
  cluster_autoscaler:
    cost: "Free (open source)"
    complexity: "Low"
    kubernetes_native: true
    spot_optimization: "Basic"
    
  spot_io:
    cost: "Percentage of savings"
    complexity: "Low"
    kubernetes_native: true
    spot_optimization: "Excellent"
    
  selection_criteria:
    - Budget constraints
    - Complexity tolerance
    - Required features
    - Support needs
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Do you understand Spot instance lifecycle and pricing?
- [ ] Can you design fault-tolerant applications for Spot?
- [ ] Do you know how to configure mixed instance fleets?
- [ ] Can you implement Spot optimization in Kubernetes?

### Practical Skills
- [ ] Can you handle Spot interruptions gracefully?
- [ ] Do you know how to configure Karpenter for Spot?
- [ ] Can you calculate Spot savings potential?
- [ ] Do you understand when to use premium tools?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete Spot fleet configurations
- Kubernetes Spot optimization setups
- Interruption handling implementations
- Cost analysis and monitoring tools

## ➡️ Next Steps
Continue to [Module 07: Rightsizing and Autoscaling](../07_rightsizing_and_autoscaling/)

---
**Module Duration**: 1 week  
**Difficulty**: Intermediate  
**Prerequisites**: Module 05 completion
