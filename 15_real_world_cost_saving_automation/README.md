# 📘 Module 15: Real-world Cost Saving Automation

## 🎯 Learning Goals
By the end of this module, you will:
- Implement automated daily idle resource scanning
- Set up intelligent auto-shutdown systems for EC2 instances
- Create Slack-integrated cost anomaly alerts
- Build Reserved Instance utilization monitoring
- Deploy comprehensive cost optimization workflows

## 📚 Table of Contents
1. [Daily Idle Resource Scanner](#daily-idle-resource-scanner)
2. [Intelligent Auto-Shutdown System](#intelligent-auto-shutdown-system)
3. [Slack Cost Anomaly Alerts](#slack-cost-anomaly-alerts)
4. [RI Utilization Monitor](#ri-utilization-monitor)
5. [Comprehensive Automation Workflows](#comprehensive-automation-workflows)
6. [Study Checkpoints](#study-checkpoints)
7. [Practical Examples](#practical-examples)

## 🔍 Daily Idle Resource Scanner

### Comprehensive Idle Resource Detection
```python
import boto3
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List
import logging

class DailyIdleResourceScanner:
    def __init__(self):
        self.ec2 = boto3.client('ec2')
        self.rds = boto3.client('rds')
        self.elbv2 = boto3.client('elbv2')
        self.cloudwatch = boto3.client('cloudwatch')
        self.sns = boto3.client('sns')
        self.s3 = boto3.client('s3')

        # Configure logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def scan_all_idle_resources(self) -> Dict:
        """Comprehensive scan of all potentially idle resources"""
        scan_results = {
            'timestamp': datetime.now().isoformat(),
            'scan_summary': {},
            'idle_resources': {
                'ec2_instances': self._scan_idle_ec2_instances(),
                'ebs_volumes': self._scan_unattached_ebs_volumes(),
                'elastic_ips': self._scan_unattached_elastic_ips(),
                'load_balancers': self._scan_idle_load_balancers(),
                'rds_instances': self._scan_idle_rds_instances(),
                'nat_gateways': self._scan_underutilized_nat_gateways(),
                's3_buckets': self._scan_empty_s3_buckets()
            },
            'cost_impact': {},
            'recommendations': []
        }

        # Calculate total cost impact
        scan_results['cost_impact'] = self._calculate_total_cost_impact(
            scan_results['idle_resources']
        )

        # Generate actionable recommendations
        scan_results['recommendations'] = self._generate_recommendations(
            scan_results['idle_resources']
        )

        # Create summary
        scan_results['scan_summary'] = self._create_scan_summary(scan_results)

        return scan_results

    def _scan_idle_ec2_instances(self, days: int = 7) -> List[Dict]:
        """Scan for idle EC2 instances based on CloudWatch metrics"""
        idle_instances = []

        # Get all running instances
        instances = self.ec2.describe_instances(
            Filters=[{'Name': 'instance-state-name', 'Values': ['running']}]
        )

        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)

        for reservation in instances['Reservations']:
            for instance in reservation['Instances']:
                instance_id = instance['InstanceId']
                instance_type = instance['InstanceType']
                launch_time = instance['LaunchTime']

                # Skip recently launched instances (less than 24 hours)
                if (datetime.utcnow().replace(tzinfo=None) -
                    launch_time.replace(tzinfo=None)).total_seconds() < 86400:
                    continue

                # Get CPU utilization
                cpu_metrics = self._get_instance_cpu_utilization(
                    instance_id, start_time, end_time
                )

                # Get network metrics
                network_metrics = self._get_instance_network_utilization(
                    instance_id, start_time, end_time
                )

                # Determine if instance is idle
                is_idle = self._is_instance_idle(cpu_metrics, network_metrics)

                if is_idle:
                    # Calculate cost impact
                    monthly_cost = self._calculate_instance_monthly_cost(
                        instance_type, instance.get('Platform', 'Linux')
                    )

                    idle_instances.append({
                        'instance_id': instance_id,
                        'instance_type': instance_type,
                        'launch_time': launch_time.isoformat(),
                        'avg_cpu_utilization': cpu_metrics.get('average', 0),
                        'avg_network_in': network_metrics.get('network_in', 0),
                        'avg_network_out': network_metrics.get('network_out', 0),
                        'monthly_cost': monthly_cost,
                        'tags': instance.get('Tags', []),
                        'recommendation': 'Consider stopping or terminating',
                        'confidence': self._calculate_idle_confidence(
                            cpu_metrics, network_metrics
                        )
                    })

        return idle_instances

    def _get_instance_cpu_utilization(self, instance_id: str,
                                    start_time: datetime,
                                    end_time: datetime) -> Dict:
        """Get CPU utilization metrics for an instance"""
        try:
            response = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/EC2',
                MetricName='CPUUtilization',
                Dimensions=[{'Name': 'InstanceId', 'Value': instance_id}],
                StartTime=start_time,
                EndTime=end_time,
                Period=3600,  # 1 hour periods
                Statistics=['Average', 'Maximum']
            )

            if response['Datapoints']:
                datapoints = response['Datapoints']
                avg_cpu = sum(dp['Average'] for dp in datapoints) / len(datapoints)
                max_cpu = max(dp['Maximum'] for dp in datapoints)

                return {
                    'average': round(avg_cpu, 2),
                    'maximum': round(max_cpu, 2),
                    'datapoints_count': len(datapoints)
                }

            return {'average': 0, 'maximum': 0, 'datapoints_count': 0}

        except Exception as e:
            self.logger.error(f"Error getting CPU metrics for {instance_id}: {e}")
            return {'average': 0, 'maximum': 0, 'datapoints_count': 0}

    def _get_instance_network_utilization(self, instance_id: str,
                                        start_time: datetime,
                                        end_time: datetime) -> Dict:
        """Get network utilization metrics for an instance"""
        try:
            # Network In
            network_in = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/EC2',
                MetricName='NetworkIn',
                Dimensions=[{'Name': 'InstanceId', 'Value': instance_id}],
                StartTime=start_time,
                EndTime=end_time,
                Period=3600,
                Statistics=['Sum']
            )

            # Network Out
            network_out = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/EC2',
                MetricName='NetworkOut',
                Dimensions=[{'Name': 'InstanceId', 'Value': instance_id}],
                StartTime=start_time,
                EndTime=end_time,
                Period=3600,
                Statistics=['Sum']
            )

            # Calculate averages
            avg_network_in = 0
            if network_in['Datapoints']:
                total_in = sum(dp['Sum'] for dp in network_in['Datapoints'])
                avg_network_in = total_in / len(network_in['Datapoints'])

            avg_network_out = 0
            if network_out['Datapoints']:
                total_out = sum(dp['Sum'] for dp in network_out['Datapoints'])
                avg_network_out = total_out / len(network_out['Datapoints'])

            return {
                'network_in': round(avg_network_in / (1024 * 1024), 2),  # Convert to MB
                'network_out': round(avg_network_out / (1024 * 1024), 2)  # Convert to MB
            }

        except Exception as e:
            self.logger.error(f"Error getting network metrics for {instance_id}: {e}")
            return {'network_in': 0, 'network_out': 0}

    def _is_instance_idle(self, cpu_metrics: Dict, network_metrics: Dict) -> bool:
        """Determine if an instance is considered idle"""
        # Define idle thresholds
        cpu_threshold = 5.0  # 5% average CPU
        network_threshold = 10.0  # 10MB average network traffic

        avg_cpu = cpu_metrics.get('average', 0)
        avg_network = (network_metrics.get('network_in', 0) +
                      network_metrics.get('network_out', 0))

        # Instance is idle if both CPU and network are below thresholds
        return avg_cpu < cpu_threshold and avg_network < network_threshold

    def _calculate_idle_confidence(self, cpu_metrics: Dict,
                                 network_metrics: Dict) -> str:
        """Calculate confidence level for idle determination"""
        avg_cpu = cpu_metrics.get('average', 0)
        max_cpu = cpu_metrics.get('maximum', 0)
        datapoints = cpu_metrics.get('datapoints_count', 0)

        # High confidence: Very low CPU, sufficient data points
        if avg_cpu < 2.0 and max_cpu < 10.0 and datapoints >= 24:
            return 'high'
        # Medium confidence: Low CPU, some data
        elif avg_cpu < 5.0 and datapoints >= 12:
            return 'medium'
        # Low confidence: Borderline metrics or insufficient data
        else:
            return 'low'

    def _scan_unattached_ebs_volumes(self) -> List[Dict]:
        """Scan for unattached EBS volumes"""
        unattached_volumes = []

        volumes = self.ec2.describe_volumes(
            Filters=[{'Name': 'status', 'Values': ['available']}]
        )

        for volume in volumes['Volumes']:
            # Calculate monthly cost
            monthly_cost = self._calculate_ebs_monthly_cost(
                volume['Size'], volume['VolumeType']
            )

            # Calculate age
            age_days = (datetime.utcnow().replace(tzinfo=None) -
                       volume['CreateTime'].replace(tzinfo=None)).days

            unattached_volumes.append({
                'volume_id': volume['VolumeId'],
                'size_gb': volume['Size'],
                'volume_type': volume['VolumeType'],
                'create_time': volume['CreateTime'].isoformat(),
                'age_days': age_days,
                'monthly_cost': monthly_cost,
                'tags': volume.get('Tags', []),
                'recommendation': 'Delete if not needed' if age_days > 7 else 'Monitor',
                'confidence': 'high' if age_days > 30 else 'medium'
            })

        return unattached_volumes

## 🤖 Intelligent Auto-Shutdown System

### Smart EC2 Auto-Shutdown
```python
import boto3
import json
from datetime import datetime, timedelta
import pytz

class IntelligentAutoShutdown:
    def __init__(self):
        self.ec2 = boto3.client('ec2')
        self.cloudwatch = boto3.client('cloudwatch')
        self.sns = boto3.client('sns')

        # Business hours configuration
        self.business_hours = {
            'start': 8,  # 8 AM
            'end': 18,   # 6 PM
            'timezone': 'US/Eastern',
            'weekdays_only': True
        }

    def intelligent_shutdown_check(self) -> Dict:
        """Perform intelligent shutdown analysis"""
        current_time = datetime.now(pytz.timezone(self.business_hours['timezone']))

        shutdown_candidates = []

        # Get all running instances
        instances = self.ec2.describe_instances(
            Filters=[{'Name': 'instance-state-name', 'Values': ['running']}]
        )

        for reservation in instances['Reservations']:
            for instance in reservation['Instances']:
                analysis = self._analyze_instance_for_shutdown(instance, current_time)

                if analysis['should_shutdown']:
                    shutdown_candidates.append(analysis)

        return {
            'timestamp': current_time.isoformat(),
            'total_instances_analyzed': self._count_total_instances(instances),
            'shutdown_candidates': shutdown_candidates,
            'potential_savings': self._calculate_shutdown_savings(shutdown_candidates)
        }

    def _analyze_instance_for_shutdown(self, instance: Dict, current_time: datetime) -> Dict:
        """Analyze individual instance for shutdown eligibility"""
        instance_id = instance['InstanceId']
        tags = {tag['Key']: tag['Value'] for tag in instance.get('Tags', [])}

        analysis = {
            'instance_id': instance_id,
            'instance_type': instance['InstanceType'],
            'tags': tags,
            'should_shutdown': False,
            'shutdown_reason': None,
            'confidence': 'low'
        }

        # Check for auto-shutdown tag
        auto_shutdown = tags.get('AutoShutdown', '').lower()
        schedule = tags.get('Schedule', '').lower()
        environment = tags.get('Environment', '').lower()

        # Rule 1: Explicit auto-shutdown enabled
        if auto_shutdown == 'enabled':
            if self._is_outside_business_hours(current_time):
                analysis.update({
                    'should_shutdown': True,
                    'shutdown_reason': 'Outside business hours (AutoShutdown=enabled)',
                    'confidence': 'high'
                })

        # Rule 2: Development/test instances outside business hours
        elif environment in ['dev', 'test', 'development', 'testing']:
            if self._is_outside_business_hours(current_time):
                analysis.update({
                    'should_shutdown': True,
                    'shutdown_reason': 'Dev/test instance outside business hours',
                    'confidence': 'medium'
                })

        # Rule 3: Business hours schedule
        elif schedule == 'business-hours':
            if self._is_outside_business_hours(current_time):
                analysis.update({
                    'should_shutdown': True,
                    'shutdown_reason': 'Business hours schedule',
                    'confidence': 'high'
                })

        # Rule 4: Idle instances (requires recent metrics analysis)
        elif self._is_instance_consistently_idle(instance_id):
            analysis.update({
                'should_shutdown': True,
                'shutdown_reason': 'Consistently idle for extended period',
                'confidence': 'medium'
            })

        return analysis

    def _is_outside_business_hours(self, current_time: datetime) -> bool:
        """Check if current time is outside business hours"""
        # Check if it's weekend and weekdays_only is True
        if self.business_hours['weekdays_only'] and current_time.weekday() >= 5:
            return True

        # Check if outside business hours
        current_hour = current_time.hour
        return (current_hour < self.business_hours['start'] or
                current_hour >= self.business_hours['end'])

    def execute_intelligent_shutdown(self, dry_run: bool = True) -> Dict:
        """Execute the intelligent shutdown process"""
        analysis = self.intelligent_shutdown_check()

        execution_results = {
            'dry_run': dry_run,
            'timestamp': datetime.now().isoformat(),
            'instances_processed': 0,
            'instances_stopped': 0,
            'errors': [],
            'notifications_sent': []
        }

        for candidate in analysis['shutdown_candidates']:
            instance_id = candidate['instance_id']
            confidence = candidate['confidence']

            # Only proceed with high confidence shutdowns in production
            if confidence == 'low':
                continue

            execution_results['instances_processed'] += 1

            try:
                if not dry_run:
                    # Stop the instance
                    self.ec2.stop_instances(InstanceIds=[instance_id])
                    execution_results['instances_stopped'] += 1

                    # Send notification
                    self._send_shutdown_notification(candidate)
                    execution_results['notifications_sent'].append(instance_id)
                else:
                    self.logger.info(f"DRY RUN: Would stop instance {instance_id}")

            except Exception as e:
                execution_results['errors'].append({
                    'instance_id': instance_id,
                    'error': str(e)
                })

        return execution_results

## 📢 Slack Cost Anomaly Alerts

### Real-time Slack Integration
```python
import boto3
import json
import requests
from datetime import datetime, timedelta

class SlackCostAnomalyAlerter:
    def __init__(self, slack_webhook_url: str):
        self.slack_webhook_url = slack_webhook_url
        self.ce = boto3.client('ce')
        self.cloudwatch = boto3.client('cloudwatch')

    def check_and_alert_anomalies(self) -> Dict:
        """Check for cost anomalies and send Slack alerts"""

        # Get cost anomalies from AWS
        anomalies = self._get_cost_anomalies()

        # Analyze spending trends
        spending_trends = self._analyze_spending_trends()

        # Check budget thresholds
        budget_alerts = self._check_budget_thresholds()

        alert_results = {
            'timestamp': datetime.now().isoformat(),
            'anomalies_found': len(anomalies),
            'alerts_sent': 0,
            'alert_details': []
        }

        # Process anomalies
        for anomaly in anomalies:
            if self._should_alert_anomaly(anomaly):
                alert_sent = self._send_anomaly_slack_alert(anomaly)
                if alert_sent:
                    alert_results['alerts_sent'] += 1
                    alert_results['alert_details'].append({
                        'type': 'anomaly',
                        'service': anomaly.get('service', 'Unknown'),
                        'impact': anomaly.get('impact', 0)
                    })

        # Process spending trends
        if spending_trends['alert_worthy']:
            alert_sent = self._send_trend_slack_alert(spending_trends)
            if alert_sent:
                alert_results['alerts_sent'] += 1
                alert_results['alert_details'].append({
                    'type': 'trend',
                    'trend': spending_trends['trend_type'],
                    'percentage': spending_trends['percentage_change']
                })

        return alert_results

    def _send_anomaly_slack_alert(self, anomaly: Dict) -> bool:
        """Send cost anomaly alert to Slack"""

        # Determine alert color based on impact
        impact = anomaly.get('impact', 0)
        if impact > 1000:
            color = '#FF0000'  # Red for high impact
            urgency = '🚨 HIGH IMPACT'
        elif impact > 500:
            color = '#FFA500'  # Orange for medium impact
            urgency = '⚠️ MEDIUM IMPACT'
        else:
            color = '#FFFF00'  # Yellow for low impact
            urgency = '💡 LOW IMPACT'

        # Create Slack message
        message = {
            "username": "AWS Cost Monitor",
            "icon_emoji": ":money_with_wings:",
            "attachments": [
                {
                    "color": color,
                    "title": f"{urgency} Cost Anomaly Detected",
                    "fields": [
                        {
                            "title": "Service",
                            "value": anomaly.get('service', 'Unknown'),
                            "short": True
                        },
                        {
                            "title": "Impact",
                            "value": f"${impact:.2f}",
                            "short": True
                        },
                        {
                            "title": "Detection Date",
                            "value": anomaly.get('date', 'Unknown'),
                            "short": True
                        },
                        {
                            "title": "Root Cause",
                            "value": anomaly.get('root_cause', 'Under investigation'),
                            "short": False
                        }
                    ],
                    "actions": [
                        {
                            "type": "button",
                            "text": "View in AWS Console",
                            "url": f"https://console.aws.amazon.com/cost-management/home#/anomaly-detection"
                        }
                    ],
                    "footer": "AWS Cost Anomaly Detection",
                    "ts": int(datetime.now().timestamp())
                }
            ]
        }

        try:
            response = requests.post(self.slack_webhook_url, json=message, timeout=10)
            return response.status_code == 200
        except Exception as e:
            print(f"Error sending Slack alert: {e}")
            return False

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Do you understand how to implement comprehensive idle resource scanning?
- [ ] Can you create intelligent auto-shutdown systems?
- [ ] Do you know how to integrate cost alerts with Slack?
- [ ] Can you build automated RI utilization monitoring?

### Practical Skills
- [ ] Can you deploy automated cost optimization workflows?
- [ ] Do you know how to configure intelligent shutdown rules?
- [ ] Can you create custom cost anomaly detection?
- [ ] Do you understand how to measure automation ROI?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete automation workflow implementations
- Slack integration templates
- Monitoring and alerting configurations
- ROI tracking and reporting tools

## ➡️ Next Steps
Continue to [Module 16: Merge Request Cost Review](../16_merge_request_cost_review/)

---
**Module Duration**: 1 week
**Difficulty**: Expert
**Prerequisites**: Module 14 completion