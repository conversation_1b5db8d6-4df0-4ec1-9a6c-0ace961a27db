# 📘 Module 13: Cost Governance and Policy Enforcement

## 🎯 Learning Goals
By the end of this module, you will:
- Implement Service Control Policies (SCPs) for cost governance
- Create IAM guardrails for cost control
- Enforce resource tagging and compliance policies
- Set up automated policy enforcement and monitoring
- Leverage both free and premium tools for governance

## 📚 Table of Contents
1. [Service Control Policies for Cost Control](#service-control-policies-for-cost-control)
2. [IAM Guardrails and Cost Controls](#iam-guardrails-and-cost-controls)
3. [Resource Tagging Enforcement](#resource-tagging-enforcement)
4. [Automated Policy Enforcement](#automated-policy-enforcement)
5. [Compliance Monitoring](#compliance-monitoring)
6. [Tools and Integration](#tools-and-integration)
7. [Study Checkpoints](#study-checkpoints)
8. [Practical Examples](#practical-examples)

## 🛡️ Service Control Policies for Cost Control

### Comprehensive Cost Control SCP
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "DenyLargeInstanceTypes",
      "Effect": "Deny",
      "Action": [
        "ec2:RunInstances"
      ],
      "Resource": "arn:aws:ec2:*:*:instance/*",
      "Condition": {
        "ForAnyValue:StringNotEquals": {
          "ec2:InstanceType": [
            "t3.nano", "t3.micro", "t3.small", "t3.medium", "t3.large",
            "t3a.nano", "t3a.micro", "t3a.small", "t3a.medium", "t3a.large",
            "m5.large", "m5.xlarge", "m5a.large", "m5a.xlarge",
            "c5.large", "c5.xlarge", "c5a.large", "c5a.xlarge"
          ]
        }
      }
    },
    {
      "Sid": "DenyExpensiveRegions",
      "Effect": "Deny",
      "Action": "*",
      "Resource": "*",
      "Condition": {
        "StringNotEquals": {
          "aws:RequestedRegion": [
            "us-east-1", "us-west-2", "eu-west-1", "ap-southeast-1"
          ]
        }
      }
    },
    {
      "Sid": "RequireResourceTagging",
      "Effect": "Deny",
      "Action": [
        "ec2:RunInstances",
        "ec2:CreateVolume",
        "rds:CreateDBInstance",
        "s3:CreateBucket"
      ],
      "Resource": "*",
      "Condition": {
        "Null": {
          "aws:RequestTag/Environment": "true",
          "aws:RequestTag/Team": "true",
          "aws:RequestTag/Project": "true",
          "aws:RequestTag/CostCenter": "true"
        }
      }
    },
    {
      "Sid": "EnforceTagValues",
      "Effect": "Deny",
      "Action": [
        "ec2:RunInstances",
        "ec2:CreateVolume",
        "rds:CreateDBInstance"
      ],
      "Resource": "*",
      "Condition": {
        "ForAllValues:StringNotEquals": {
          "aws:RequestTag/Environment": ["prod", "staging", "dev", "test"]
        }
      }
    },
    {
      "Sid": "DenyRDSWithoutEncryption",
      "Effect": "Deny",
      "Action": [
        "rds:CreateDBInstance",
        "rds:CreateDBCluster"
      ],
      "Resource": "*",
      "Condition": {
        "Bool": {
          "rds:StorageEncrypted": "false"
        }
      }
    },
    {
      "Sid": "LimitEBSVolumeSize",
      "Effect": "Deny",
      "Action": [
        "ec2:CreateVolume"
      ],
      "Resource": "*",
      "Condition": {
        "NumericGreaterThan": {
          "ec2:VolumeSize": "1000"
        }
      }
    },
    {
      "Sid": "DenyReservedInstanceModification",
      "Effect": "Deny",
      "Action": [
        "ec2:ModifyReservedInstances",
        "ec2:PurchaseReservedInstancesOffering"
      ],
      "Resource": "*",
      "Condition": {
        "StringNotEquals": {
          "aws:PrincipalTag/Role": "FinOpsAdmin"
        }
      }
    },
    {
      "Sid": "RequireApprovalForExpensiveResources",
      "Effect": "Deny",
      "Action": [
        "ec2:RunInstances"
      ],
      "Resource": "arn:aws:ec2:*:*:instance/*",
      "Condition": {
        "ForAnyValue:StringEquals": {
          "ec2:InstanceType": [
            "m5.2xlarge", "m5.4xlarge", "m5.8xlarge", "m5.12xlarge",
            "c5.2xlarge", "c5.4xlarge", "c5.9xlarge", "c5.18xlarge"
          ]
        },
        "StringNotEquals": {
          "aws:RequestTag/ApprovedBy": "FinOpsTeam"
        }
      }
    }
  ]
}
```

### Environment-Specific SCPs
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "ProductionEnvironmentControls",
      "Effect": "Deny",
      "Action": [
        "ec2:TerminateInstances",
        "rds:DeleteDBInstance",
        "s3:DeleteBucket"
      ],
      "Resource": "*",
      "Condition": {
        "StringEquals": {
          "aws:ResourceTag/Environment": "prod"
        },
        "StringNotEquals": {
          "aws:PrincipalTag/Role": ["ProductionAdmin", "FinOpsAdmin"]
        }
      }
    },
    {
      "Sid": "DevelopmentResourceLimits",
      "Effect": "Deny",
      "Action": [
        "ec2:RunInstances"
      ],
      "Resource": "arn:aws:ec2:*:*:instance/*",
      "Condition": {
        "StringEquals": {
          "aws:RequestTag/Environment": "dev"
        },
        "ForAnyValue:StringNotEquals": {
          "ec2:InstanceType": [
            "t3.nano", "t3.micro", "t3.small", "t3.medium"
          ]
        }
      }
    },
    {
      "Sid": "StagingBudgetControls",
      "Effect": "Deny",
      "Action": [
        "ec2:RunInstances",
        "rds:CreateDBInstance"
      ],
      "Resource": "*",
      "Condition": {
        "StringEquals": {
          "aws:RequestTag/Environment": "staging"
        },
        "DateGreaterThan": {
          "aws:CurrentTime": "2024-12-31T23:59:59Z"
        }
      }
    }
  ]
}
```

## 🔐 IAM Guardrails and Cost Controls

### Cost-Aware IAM Policies
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowBasicEC2Operations",
      "Effect": "Allow",
      "Action": [
        "ec2:DescribeInstances",
        "ec2:DescribeImages",
        "ec2:DescribeSecurityGroups",
        "ec2:DescribeSubnets",
        "ec2:DescribeVpcs"
      ],
      "Resource": "*"
    },
    {
      "Sid": "AllowInstanceManagementWithinTeam",
      "Effect": "Allow",
      "Action": [
        "ec2:StartInstances",
        "ec2:StopInstances",
        "ec2:RebootInstances"
      ],
      "Resource": "*",
      "Condition": {
        "StringEquals": {
          "ec2:ResourceTag/Team": "${aws:PrincipalTag/Team}"
        }
      }
    },
    {
      "Sid": "AllowInstanceCreationWithBudgetLimit",
      "Effect": "Allow",
      "Action": [
        "ec2:RunInstances"
      ],
      "Resource": [
        "arn:aws:ec2:*:*:instance/*",
        "arn:aws:ec2:*:*:volume/*",
        "arn:aws:ec2:*:*:network-interface/*"
      ],
      "Condition": {
        "StringEquals": {
          "aws:RequestTag/Team": "${aws:PrincipalTag/Team}",
          "aws:RequestTag/Environment": ["dev", "test"]
        },
        "ForAnyValue:StringEquals": {
          "ec2:InstanceType": [
            "t3.nano", "t3.micro", "t3.small", "t3.medium"
          ]
        },
        "NumericLessThan": {
          "aws:RequestTag/MonthlyCostLimit": "500"
        }
      }
    },
    {
      "Sid": "DenyTerminationOfProductionResources",
      "Effect": "Deny",
      "Action": [
        "ec2:TerminateInstances",
        "rds:DeleteDBInstance"
      ],
      "Resource": "*",
      "Condition": {
        "StringEquals": {
          "aws:ResourceTag/Environment": "prod"
        },
        "StringNotEquals": {
          "aws:PrincipalTag/Role": "ProductionAdmin"
        }
      }
    },
    {
      "Sid": "RequireManagerApprovalForExpensiveOperations",
      "Effect": "Deny",
      "Action": [
        "ec2:RunInstances"
      ],
      "Resource": "*",
      "Condition": {
        "ForAnyValue:StringEquals": {
          "ec2:InstanceType": [
            "m5.xlarge", "m5.2xlarge", "c5.xlarge", "c5.2xlarge"
          ]
        },
        "StringNotEquals": {
          "aws:RequestTag/ManagerApproval": "approved"
        }
      }
    }
  ]
}
```

### Dynamic IAM Policies with Cost Context
```python
import boto3
import json
from datetime import datetime, timedelta
from typing import Dict, List

class DynamicCostPolicyManager:
    def __init__(self):
        self.iam = boto3.client('iam')
        self.ce = boto3.client('ce')
        self.organizations = boto3.client('organizations')
        
    def generate_cost_aware_policy(self, team: str, monthly_budget: float) -> Dict:
        """Generate IAM policy based on team's budget and current spend"""
        
        # Get current month's spend for the team
        current_spend = self._get_team_current_spend(team)
        budget_utilization = (current_spend / monthly_budget) * 100
        
        # Generate policy based on budget utilization
        if budget_utilization < 50:
            # Low utilization - allow normal operations
            policy = self._generate_normal_policy(team)
        elif budget_utilization < 80:
            # Medium utilization - restrict expensive operations
            policy = self._generate_restricted_policy(team)
        else:
            # High utilization - emergency restrictions
            policy = self._generate_emergency_policy(team)
        
        return {
            'team': team,
            'monthly_budget': monthly_budget,
            'current_spend': current_spend,
            'budget_utilization': budget_utilization,
            'policy': policy,
            'restrictions_level': self._get_restriction_level(budget_utilization)
        }
    
    def _get_team_current_spend(self, team: str) -> float:
        """Get current month's spend for a team"""
        start_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        try:
            response = self.ce.get_cost_and_usage(
                TimePeriod={'Start': start_date, 'End': end_date},
                Granularity='MONTHLY',
                Metrics=['BlendedCost'],
                Filter={
                    'Tags': {
                        'Key': 'Team',
                        'Values': [team]
                    }
                }
            )
            
            total_cost = 0
            for result in response['ResultsByTime']:
                for group in result['Groups']:
                    cost = float(group['Metrics']['BlendedCost']['Amount'])
                    total_cost += cost
            
            return total_cost
            
        except Exception as e:
            print(f"Error getting team spend: {e}")
            return 0.0
    
    def _generate_normal_policy(self, team: str) -> Dict:
        """Generate normal operations policy"""
        return {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "AllowNormalOperations",
                    "Effect": "Allow",
                    "Action": [
                        "ec2:RunInstances",
                        "ec2:StartInstances",
                        "ec2:StopInstances",
                        "rds:CreateDBInstance",
                        "s3:CreateBucket"
                    ],
                    "Resource": "*",
                    "Condition": {
                        "StringEquals": {
                            "aws:RequestTag/Team": team
                        },
                        "ForAnyValue:StringEquals": {
                            "ec2:InstanceType": [
                                "t3.nano", "t3.micro", "t3.small", "t3.medium", "t3.large",
                                "m5.large", "m5.xlarge", "c5.large", "c5.xlarge"
                            ]
                        }
                    }
                }
            ]
        }
    
    def _generate_restricted_policy(self, team: str) -> Dict:
        """Generate restricted operations policy"""
        return {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "AllowRestrictedOperations",
                    "Effect": "Allow",
                    "Action": [
                        "ec2:RunInstances",
                        "ec2:StartInstances",
                        "ec2:StopInstances"
                    ],
                    "Resource": "*",
                    "Condition": {
                        "StringEquals": {
                            "aws:RequestTag/Team": team
                        },
                        "ForAnyValue:StringEquals": {
                            "ec2:InstanceType": [
                                "t3.nano", "t3.micro", "t3.small", "t3.medium"
                            ]
                        }
                    }
                },
                {
                    "Sid": "DenyExpensiveOperations",
                    "Effect": "Deny",
                    "Action": [
                        "rds:CreateDBInstance",
                        "ec2:RunInstances"
                    ],
                    "Resource": "*",
                    "Condition": {
                        "ForAnyValue:StringEquals": {
                            "ec2:InstanceType": [
                                "m5.xlarge", "m5.2xlarge", "c5.xlarge", "c5.2xlarge"
                            ]
                        }
                    }
                }
            ]
        }
    
    def _generate_emergency_policy(self, team: str) -> Dict:
        """Generate emergency restrictions policy"""
        return {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "EmergencyRestrictionsOnly",
                    "Effect": "Allow",
                    "Action": [
                        "ec2:DescribeInstances",
                        "ec2:StopInstances",
                        "ec2:TerminateInstances"
                    ],
                    "Resource": "*",
                    "Condition": {
                        "StringEquals": {
                            "ec2:ResourceTag/Team": team
                        }
                    }
                },
                {
                    "Sid": "DenyAllCreationOperations",
                    "Effect": "Deny",
                    "Action": [
                        "ec2:RunInstances",
                        "rds:CreateDBInstance",
                        "s3:CreateBucket"
                    ],
                    "Resource": "*"
                }
            ]
        }
    
    def _get_restriction_level(self, budget_utilization: float) -> str:
        """Get restriction level based on budget utilization"""
        if budget_utilization < 50:
            return "normal"
        elif budget_utilization < 80:
            return "restricted"
        else:
            return "emergency"
    
    def update_team_policies(self, teams_budgets: Dict[str, float]):
        """Update IAM policies for all teams based on their budget status"""
        results = []
        
        for team, budget in teams_budgets.items():
            policy_config = self.generate_cost_aware_policy(team, budget)
            
            # Update IAM policy (implementation would depend on your IAM structure)
            policy_name = f"CostAware-{team}-Policy"
            
            try:
                # This is a simplified example - actual implementation would be more complex
                self.iam.put_user_policy(
                    UserName=f"{team}-user",  # Assuming team-based users
                    PolicyName=policy_name,
                    PolicyDocument=json.dumps(policy_config['policy'])
                )
                
                results.append({
                    'team': team,
                    'status': 'success',
                    'restriction_level': policy_config['restrictions_level'],
                    'budget_utilization': policy_config['budget_utilization']
                })
                
            except Exception as e:
                results.append({
                    'team': team,
                    'status': 'error',
                    'error': str(e)
                })
        
        return results
```

## 🏷️ Resource Tagging Enforcement

### Automated Tagging Compliance
```python
import boto3
import json
from datetime import datetime
from typing import Dict, List

class TaggingComplianceEnforcer:
    def __init__(self):
        self.ec2 = boto3.client('ec2')
        self.rds = boto3.client('rds')
        self.s3 = boto3.client('s3')
        self.lambda_client = boto3.client('lambda')
        self.sns = boto3.client('sns')
        
        self.required_tags = ['Environment', 'Team', 'Project', 'CostCenter', 'Owner']
        
    def scan_resource_compliance(self) -> Dict:
        """Scan all resources for tagging compliance"""
        compliance_report = {
            'timestamp': datetime.now().isoformat(),
            'ec2_instances': self._check_ec2_compliance(),
            'ebs_volumes': self._check_ebs_compliance(),
            'rds_instances': self._check_rds_compliance(),
            's3_buckets': self._check_s3_compliance(),
            'summary': {}
        }
        
        # Calculate summary statistics
        total_resources = 0
        compliant_resources = 0
        
        for resource_type, resources in compliance_report.items():
            if isinstance(resources, list):
                total_resources += len(resources)
                compliant_resources += sum(1 for r in resources if r['compliant'])
        
        compliance_report['summary'] = {
            'total_resources': total_resources,
            'compliant_resources': compliant_resources,
            'compliance_percentage': round((compliant_resources / total_resources * 100), 2) if total_resources > 0 else 0,
            'non_compliant_resources': total_resources - compliant_resources
        }
        
        return compliance_report
    
    def _check_ec2_compliance(self) -> List[Dict]:
        """Check EC2 instances for tag compliance"""
        instances = self.ec2.describe_instances()
        compliance_results = []
        
        for reservation in instances['Reservations']:
            for instance in reservation['Instances']:
                instance_id = instance['InstanceId']
                tags = {tag['Key']: tag['Value'] for tag in instance.get('Tags', [])}
                
                missing_tags = [tag for tag in self.required_tags if tag not in tags]
                
                compliance_results.append({
                    'resource_id': instance_id,
                    'resource_type': 'EC2Instance',
                    'tags': tags,
                    'missing_tags': missing_tags,
                    'compliant': len(missing_tags) == 0,
                    'launch_time': instance['LaunchTime'].isoformat(),
                    'state': instance['State']['Name']
                })
        
        return compliance_results
    
    def _check_ebs_compliance(self) -> List[Dict]:
        """Check EBS volumes for tag compliance"""
        volumes = self.ec2.describe_volumes()
        compliance_results = []
        
        for volume in volumes['Volumes']:
            volume_id = volume['VolumeId']
            tags = {tag['Key']: tag['Value'] for tag in volume.get('Tags', [])}
            
            missing_tags = [tag for tag in self.required_tags if tag not in tags]
            
            compliance_results.append({
                'resource_id': volume_id,
                'resource_type': 'EBSVolume',
                'tags': tags,
                'missing_tags': missing_tags,
                'compliant': len(missing_tags) == 0,
                'create_time': volume['CreateTime'].isoformat(),
                'state': volume['State']
            })
        
        return compliance_results
    
    def _check_rds_compliance(self) -> List[Dict]:
        """Check RDS instances for tag compliance"""
        try:
            instances = self.rds.describe_db_instances()
            compliance_results = []
            
            for instance in instances['DBInstances']:
                instance_id = instance['DBInstanceIdentifier']
                
                # Get tags for RDS instance
                tags_response = self.rds.list_tags_for_resource(
                    ResourceName=instance['DBInstanceArn']
                )
                tags = {tag['Key']: tag['Value'] for tag in tags_response['TagList']}
                
                missing_tags = [tag for tag in self.required_tags if tag not in tags]
                
                compliance_results.append({
                    'resource_id': instance_id,
                    'resource_type': 'RDSInstance',
                    'tags': tags,
                    'missing_tags': missing_tags,
                    'compliant': len(missing_tags) == 0,
                    'create_time': instance['InstanceCreateTime'].isoformat(),
                    'status': instance['DBInstanceStatus']
                })
            
            return compliance_results
            
        except Exception as e:
            print(f"Error checking RDS compliance: {e}")
            return []
    
    def _check_s3_compliance(self) -> List[Dict]:
        """Check S3 buckets for tag compliance"""
        try:
            buckets = self.s3.list_buckets()
            compliance_results = []
            
            for bucket in buckets['Buckets']:
                bucket_name = bucket['Name']
                
                try:
                    # Get bucket tags
                    tags_response = self.s3.get_bucket_tagging(Bucket=bucket_name)
                    tags = {tag['Key']: tag['Value'] for tag in tags_response['TagSet']}
                except self.s3.exceptions.ClientError:
                    # No tags set
                    tags = {}
                
                missing_tags = [tag for tag in self.required_tags if tag not in tags]
                
                compliance_results.append({
                    'resource_id': bucket_name,
                    'resource_type': 'S3Bucket',
                    'tags': tags,
                    'missing_tags': missing_tags,
                    'compliant': len(missing_tags) == 0,
                    'create_time': bucket['CreationDate'].isoformat()
                })
            
            return compliance_results
            
        except Exception as e:
            print(f"Error checking S3 compliance: {e}")
            return []
    
    def remediate_non_compliant_resources(self, compliance_report: Dict, 
                                        auto_tag: bool = False) -> Dict:
        """Remediate non-compliant resources"""
        remediation_results = {
            'auto_tagged': [],
            'notifications_sent': [],
            'errors': []
        }
        
        for resource_type, resources in compliance_report.items():
            if isinstance(resources, list):
                for resource in resources:
                    if not resource['compliant']:
                        try:
                            if auto_tag:
                                # Apply default tags
                                self._apply_default_tags(resource)
                                remediation_results['auto_tagged'].append(resource['resource_id'])
                            else:
                                # Send notification to resource owner
                                self._send_compliance_notification(resource)
                                remediation_results['notifications_sent'].append(resource['resource_id'])
                                
                        except Exception as e:
                            remediation_results['errors'].append({
                                'resource_id': resource['resource_id'],
                                'error': str(e)
                            })
        
        return remediation_results
    
    def _apply_default_tags(self, resource: Dict):
        """Apply default tags to non-compliant resource"""
        default_tags = {
            'Environment': 'unknown',
            'Team': 'unassigned',
            'Project': 'unassigned',
            'CostCenter': 'unassigned',
            'Owner': 'unassigned',
            'AutoTagged': 'true',
            'ComplianceDate': datetime.now().strftime('%Y-%m-%d')
        }
        
        resource_id = resource['resource_id']
        resource_type = resource['resource_type']
        
        # Apply tags based on resource type
        if resource_type == 'EC2Instance':
            self.ec2.create_tags(
                Resources=[resource_id],
                Tags=[{'Key': k, 'Value': v} for k, v in default_tags.items()]
            )
        elif resource_type == 'EBSVolume':
            self.ec2.create_tags(
                Resources=[resource_id],
                Tags=[{'Key': k, 'Value': v} for k, v in default_tags.items()]
            )
        elif resource_type == 'S3Bucket':
            self.s3.put_bucket_tagging(
                Bucket=resource_id,
                Tagging={'TagSet': [{'Key': k, 'Value': v} for k, v in default_tags.items()]}
            )
    
    def _send_compliance_notification(self, resource: Dict):
        """Send compliance notification"""
        message = f"""
        Resource Tagging Compliance Alert
        
        Resource ID: {resource['resource_id']}
        Resource Type: {resource['resource_type']}
        Missing Tags: {', '.join(resource['missing_tags'])}
        
        Please add the required tags to ensure proper cost allocation and governance.
        
        Required tags: {', '.join(self.required_tags)}
        """
        
        # Send SNS notification (configure topic ARN)
        self.sns.publish(
            TopicArn='arn:aws:sns:us-east-1:123456789012:tagging-compliance',
            Subject='Resource Tagging Compliance Alert',
            Message=message
        )
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Do you understand how to create effective SCPs for cost control?
- [ ] Can you implement IAM guardrails for cost governance?
- [ ] Do you know how to enforce resource tagging policies?
- [ ] Can you set up automated compliance monitoring?

### Practical Skills
- [ ] Can you create comprehensive cost governance policies?
- [ ] Do you know how to implement dynamic policy management?
- [ ] Can you automate compliance remediation?
- [ ] Do you understand governance tool selection?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete SCP policy templates
- IAM guardrail implementations
- Automated compliance scripts
- Governance monitoring setups

## ➡️ Next Steps
Continue to [Module 14: Open Source and 3rd Party Tools](../14_open_source_and_3rd_party_tools/)

---
**Module Duration**: 1 week  
**Difficulty**: Advanced  
**Prerequisites**: Module 12 completion
