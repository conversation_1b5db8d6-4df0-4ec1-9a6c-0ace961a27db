# 📘 Module 04: Tagging and Resource Ownership

## 🎯 Learning Goals
By the end of this module, you will:
- Design and implement comprehensive tagging strategies
- Automate tagging using Lambda and EventBridge
- Enforce tagging policies using SCPs and CEL
- Set up cost allocation based on tags
- Integrate with both free and premium tagging tools

## 📚 Table of Contents
1. [Tag Standardization and Best Practices](#tag-standardization-and-best-practices)
2. [Automated Tagging with Lambda](#automated-tagging-with-lambda)
3. [Policy Enforcement](#policy-enforcement)
4. [Cost Allocation Implementation](#cost-allocation-implementation)
5. [Tools and Integration](#tools-and-integration)
6. [Study Checkpoints](#study-checkpoints)
7. [Practical Examples](#practical-examples)

## 🏷️ Tag Standardization and Best Practices

### Core Tagging Framework
```yaml
tagging_framework:
  mandatory_tags:
    Environment:
      values: ["prod", "staging", "dev", "test"]
      purpose: "Cost allocation and environment separation"
      
    Team:
      values: ["backend", "frontend", "data", "devops", "security"]
      purpose: "Team ownership and chargeback"
      
    Project:
      format: "lowercase-with-hyphens"
      purpose: "Project-level cost tracking"
      
    CostCenter:
      values: ["engineering", "marketing", "sales", "operations"]
      purpose: "Financial cost center allocation"
      
    Owner:
      format: "<EMAIL>"
      purpose: "Technical contact for resources"

  optional_tags:
    Application: "Service or application name"
    Version: "Application version (semantic versioning)"
    Schedule: "Resource usage schedule for automation"
    Backup: "Backup requirements and frequency"
    Compliance: "Compliance requirements (PCI, HIPAA, etc.)"
```

### Naming Conventions
- **Consistent casing**: Use PascalCase for tag keys, lowercase for values
- **No spaces**: Use hyphens or underscores for separation
- **Descriptive**: Tag names should be self-explanatory
- **Standardized**: Use predefined value lists where possible

### Tag Hierarchy and Inheritance
```mermaid
graph TD
    A[Account Level] --> B[VPC Level]
    B --> C[Subnet Level]
    C --> D[Resource Level]
    
    A1[CostCenter<br/>Environment] --> A
    B1[Team<br/>Project] --> B
    C1[Application<br/>Component] --> C
    D1[Owner<br/>Schedule] --> D
```

## 🤖 Automated Tagging with Lambda

### EventBridge-Triggered Tagging
```python
import json
import boto3
from datetime import datetime

def lambda_handler(event, context):
    """
    Auto-tag resources when they are created
    Triggered by CloudWatch Events/EventBridge
    """
    
    # Initialize AWS clients
    ec2 = boto3.client('ec2')
    sts = boto3.client('sts')
    
    # Extract event details
    detail = event['detail']
    event_name = detail['eventName']
    user_identity = detail['userIdentity']
    
    # Get account information
    account_id = sts.get_caller_identity()['Account']
    
    # Default tags to apply
    default_tags = [
        {
            'Key': 'CreatedBy',
            'Value': user_identity.get('userName', 'Unknown')
        },
        {
            'Key': 'CreatedDate',
            'Value': datetime.now().strftime('%Y-%m-%d')
        },
        {
            'Key': 'AccountId',
            'Value': account_id
        }
    ]
    
    # Handle different resource types
    if event_name == 'RunInstances':
        # Tag EC2 instances
        instances = detail['responseElements']['instancesSet']['items']
        for instance in instances:
            instance_id = instance['instanceId']
            
            # Add instance-specific tags
            instance_tags = default_tags + [
                {
                    'Key': 'ResourceType',
                    'Value': 'EC2Instance'
                },
                {
                    'Key': 'InstanceType',
                    'Value': instance['instanceType']
                }
            ]
            
            # Apply tags
            ec2.create_tags(
                Resources=[instance_id],
                Tags=instance_tags
            )
            
    elif event_name == 'CreateBucket':
        # Tag S3 buckets
        s3 = boto3.client('s3')
        bucket_name = detail['requestParameters']['bucketName']
        
        bucket_tags = [
            {
                'Key': tag['Key'],
                'Value': tag['Value']
            } for tag in default_tags
        ] + [
            {
                'Key': 'ResourceType',
                'Value': 'S3Bucket'
            }
        ]
        
        s3.put_bucket_tagging(
            Bucket=bucket_name,
            Tagging={
                'TagSet': bucket_tags
            }
        )
    
    return {
        'statusCode': 200,
        'body': json.dumps('Tagging completed successfully')
    }
```

### Terraform Configuration for Auto-Tagging
```hcl
# EventBridge Rule for Resource Creation
resource "aws_cloudwatch_event_rule" "resource_creation" {
  name        = "resource-creation-tagging"
  description = "Trigger auto-tagging on resource creation"

  event_pattern = jsonencode({
    source      = ["aws.ec2", "aws.s3", "aws.rds"]
    detail-type = ["AWS API Call via CloudTrail"]
    detail = {
      eventSource = ["ec2.amazonaws.com", "s3.amazonaws.com", "rds.amazonaws.com"]
      eventName   = ["RunInstances", "CreateBucket", "CreateDBInstance"]
    }
  })
}

# EventBridge Target
resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.resource_creation.name
  target_id = "AutoTaggingLambdaTarget"
  arn       = aws_lambda_function.auto_tagging.arn
}

# Lambda Permission for EventBridge
resource "aws_lambda_permission" "allow_eventbridge" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auto_tagging.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.resource_creation.arn
}

# Auto-tagging Lambda Function
resource "aws_lambda_function" "auto_tagging" {
  filename         = "auto_tagging.zip"
  function_name    = "resource-auto-tagging"
  role            = aws_iam_role.auto_tagging_role.arn
  handler         = "index.lambda_handler"
  runtime         = "python3.9"
  timeout         = 60

  environment {
    variables = {
      DEFAULT_ENVIRONMENT = "dev"
      DEFAULT_TEAM       = "platform"
    }
  }
}
```

## 🛡️ Policy Enforcement

### Service Control Policy (SCP) for Tag Enforcement
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "RequireTagsOnResourceCreation",
      "Effect": "Deny",
      "Action": [
        "ec2:RunInstances",
        "ec2:CreateVolume",
        "ec2:CreateSnapshot",
        "rds:CreateDBInstance",
        "s3:CreateBucket"
      ],
      "Resource": "*",
      "Condition": {
        "Null": {
          "aws:RequestedRegion": "false"
        },
        "ForAnyValue:StringNotEquals": {
          "aws:RequestTag/Environment": ["prod", "staging", "dev", "test"]
        }
      }
    },
    {
      "Sid": "RequireMandatoryTags",
      "Effect": "Deny",
      "Action": [
        "ec2:RunInstances",
        "rds:CreateDBInstance",
        "s3:CreateBucket"
      ],
      "Resource": "*",
      "Condition": {
        "Null": {
          "aws:RequestTag/Environment": "true",
          "aws:RequestTag/Team": "true",
          "aws:RequestTag/Project": "true",
          "aws:RequestTag/Owner": "true"
        }
      }
    }
  ]
}
```

### IAM Policy for Tag-Based Access Control
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ec2:DescribeInstances",
        "ec2:DescribeVolumes",
        "ec2:DescribeSnapshots"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "ec2:StartInstances",
        "ec2:StopInstances",
        "ec2:RebootInstances",
        "ec2:TerminateInstances"
      ],
      "Resource": "*",
      "Condition": {
        "StringEquals": {
          "ec2:ResourceTag/Team": "${aws:PrincipalTag/Team}"
        }
      }
    }
  ]
}
```

### CloudFormation Guard Rules
```
# CFN Guard rules for tag validation
let ec2_instances = Resources.*[ Type == 'AWS::EC2::Instance' ]

rule EC2_MUST_HAVE_REQUIRED_TAGS when %ec2_instances !empty {
    %ec2_instances.Properties.Tags exists
    %ec2_instances.Properties.Tags[*] {
        Key == "Environment" OR
        Key == "Team" OR  
        Key == "Project" OR
        Key == "Owner"
    }
}

rule ENVIRONMENT_TAG_VALUES when %ec2_instances !empty {
    %ec2_instances.Properties.Tags[*] {
        when Key == "Environment" {
            Value in ["prod", "staging", "dev", "test"]
        }
    }
}
```

## 💰 Cost Allocation Implementation

### Tag-Based Cost Allocation Rules
```yaml
cost_allocation_rules:
  primary_allocation:
    dimension: "CostCenter"
    method: "direct"
    
  secondary_allocation:
    dimension: "Team"
    method: "proportional"
    basis: "usage_hours"
    
  tertiary_allocation:
    dimension: "Project"
    method: "activity_based"
    basis: "resource_count"

  shared_services_allocation:
    services: ["vpc", "nat-gateway", "load-balancer"]
    allocation_method: "proportional"
    allocation_basis: "team_resource_usage"
    
    rules:
      - if_tag: "Team=backend"
        percentage: 40
      - if_tag: "Team=frontend" 
        percentage: 30
      - if_tag: "Team=data"
        percentage: 20
      - if_tag: "Team=devops"
        percentage: 10
```

### Cost Allocation Report Generation
```python
import boto3
import pandas as pd
from datetime import datetime, timedelta

def generate_cost_allocation_report():
    """
    Generate cost allocation report based on tags
    """
    ce_client = boto3.client('ce')
    
    # Define time period (last month)
    end_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
    start_date = (datetime.now().replace(day=1) - timedelta(days=1)).replace(day=1).strftime('%Y-%m-%d')
    
    # Get cost data grouped by tags
    response = ce_client.get_cost_and_usage(
        TimePeriod={
            'Start': start_date,
            'End': end_date
        },
        Granularity='MONTHLY',
        Metrics=['BlendedCost'],
        GroupBy=[
            {
                'Type': 'TAG',
                'Key': 'Team'
            },
            {
                'Type': 'TAG', 
                'Key': 'Environment'
            },
            {
                'Type': 'TAG',
                'Key': 'Project'
            }
        ]
    )
    
    # Process results into DataFrame
    cost_data = []
    for result in response['ResultsByTime']:
        for group in result['Groups']:
            team = group['Keys'][0] if group['Keys'][0] else 'Untagged'
            environment = group['Keys'][1] if group['Keys'][1] else 'Untagged'
            project = group['Keys'][2] if group['Keys'][2] else 'Untagged'
            cost = float(group['Metrics']['BlendedCost']['Amount'])
            
            cost_data.append({
                'Team': team,
                'Environment': environment,
                'Project': project,
                'Cost': cost,
                'Period': result['TimePeriod']['Start']
            })
    
    df = pd.DataFrame(cost_data)
    
    # Generate summary reports
    team_summary = df.groupby('Team')['Cost'].sum().sort_values(ascending=False)
    environment_summary = df.groupby('Environment')['Cost'].sum().sort_values(ascending=False)
    project_summary = df.groupby('Project')['Cost'].sum().sort_values(ascending=False)
    
    return {
        'detailed_data': df,
        'team_summary': team_summary,
        'environment_summary': environment_summary,
        'project_summary': project_summary
    }
```

## 🛠️ Tools and Integration

### Free Tools

#### AWS Resource Groups Tag Editor
- **Bulk tagging** of existing resources
- **Tag-based resource grouping**
- **Cross-service tag management**
- **Tag compliance reporting**

#### AWS Config Rules for Tag Compliance
```yaml
config_rules:
  required_tags_rule:
    name: "required-tags-compliance"
    source: "AWS_CONFIG_MANAGED_RULE"
    identifier: "REQUIRED_TAGS"
    
    parameters:
      tag1Key: "Environment"
      tag1Value: "prod,staging,dev,test"
      tag2Key: "Team"
      tag3Key: "Project"
      tag4Key: "Owner"
```

### Premium Tools

#### CloudHealth by VMware
- **Advanced tag analytics** and reporting
- **Tag-based cost allocation** with custom rules
- **Tag governance** and compliance monitoring
- **Multi-cloud tag management**

#### Harness Cloud Cost Management
- **Intelligent tag suggestions** based on usage patterns
- **Automated tag propagation** across resources
- **Tag-based budget** and alert management
- **Cost optimization** recommendations by tags

### Tool Selection Criteria
```yaml
tool_selection:
  free_tools_suitable_for:
    - Small to medium organizations
    - Basic tag compliance needs
    - AWS-only environments
    - Limited budget for tooling
    
  premium_tools_suitable_for:
    - Large enterprises
    - Complex tag governance requirements
    - Multi-cloud environments
    - Advanced analytics needs
    
  evaluation_criteria:
    - Organization size and complexity
    - Multi-cloud requirements
    - Budget for tooling
    - Integration requirements
    - Compliance needs
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Can you design a comprehensive tagging strategy?
- [ ] Do you understand tag enforcement mechanisms?
- [ ] Can you implement automated tagging solutions?
- [ ] Do you know how to set up tag-based cost allocation?

### Practical Skills
- [ ] Can you create SCP policies for tag enforcement?
- [ ] Do you know how to automate tagging with Lambda?
- [ ] Can you generate cost allocation reports from tags?
- [ ] Do you understand when to use premium tagging tools?

### Hands-on Exercises
- [ ] Implement a complete tagging strategy for a sample environment
- [ ] Create automated tagging Lambda functions
- [ ] Set up tag compliance monitoring
- [ ] Generate cost allocation reports

## 📋 Practical Examples

See the `examples/` directory for:
- Complete tagging automation scripts
- SCP policy templates
- Cost allocation report generators
- Tag compliance monitoring tools

## ➡️ Next Steps
Continue to [Module 05: Idle Resources and Automated Cleanup](../05_idle_resources_and_automated_cleanup/)

---
**Module Duration**: 1 week  
**Difficulty**: Intermediate  
**Prerequisites**: Module 03 completion
