# 📘 Module 05: Idle Resources and Automated Cleanup

## 🎯 Learning Goals
By the end of this module, you will:
- Identify and detect idle AWS resources across all services
- Implement automated cleanup scripts using Lambda and EventBridge
- Use CUR queries to find cost optimization opportunities
- Set up monitoring and alerting for resource waste
- Leverage both free and premium tools for resource optimization

## 📚 Table of Contents
1. [Detecting Idle Resources](#detecting-idle-resources)
2. [Automated Cleanup Strategies](#automated-cleanup-strategies)
3. [CUR Query Examples](#cur-query-examples)
4. [Monitoring and Alerting](#monitoring-and-alerting)
5. [Tools and Integration](#tools-and-integration)
6. [Study Checkpoints](#study-checkpoints)
7. [Practical Examples](#practical-examples)

## 🔍 Detecting Idle Resources

### Common Idle Resource Types

#### EC2 Instances
```yaml
idle_ec2_detection:
  criteria:
    cpu_utilization: "<5% for 7+ days"
    network_in: "<1MB for 7+ days"
    network_out: "<1MB for 7+ days"
    status_check: "passing"
    
  detection_methods:
    cloudwatch_metrics:
      - CPUUtilization
      - NetworkIn
      - NetworkOut
      - StatusCheckFailed
      
    aws_compute_optimizer:
      - Underutilized instances
      - Idle instances
      - Rightsizing recommendations
```

#### EBS Volumes
```yaml
idle_ebs_detection:
  unattached_volumes:
    criteria: "Volume state = available"
    cost_impact: "100% waste"
    
  low_usage_volumes:
    criteria:
      - "VolumeReadOps <100/day for 30+ days"
      - "VolumeWriteOps <100/day for 30+ days"
    cost_impact: "Potential rightsizing opportunity"
    
  snapshot_cleanup:
    criteria: "Snapshots >90 days old with no AMI reference"
    cost_impact: "Storage cost reduction"
```

#### Load Balancers
```yaml
idle_load_balancer_detection:
  application_load_balancer:
    criteria:
      - "RequestCount = 0 for 7+ days"
      - "TargetResponseTime = N/A"
      - "No healthy targets"
      
  network_load_balancer:
    criteria:
      - "NewFlowCount = 0 for 7+ days"
      - "ProcessedBytes = 0"
      
  classic_load_balancer:
    criteria:
      - "RequestCount = 0 for 7+ days"
      - "No registered instances"
```

#### RDS Instances
```yaml
idle_rds_detection:
  criteria:
    database_connections: "0 connections for 7+ days"
    cpu_utilization: "<5% for 7+ days"
    read_iops: "<10/day for 7+ days"
    write_iops: "<10/day for 7+ days"
    
  detection_queries:
    - DatabaseConnections
    - CPUUtilization
    - ReadIOPS
    - WriteIOPS
    - FreeableMemory
```

### Detection Automation Script
```python
import boto3
import json
from datetime import datetime, timedelta
from typing import List, Dict

class IdleResourceDetector:
    def __init__(self):
        self.ec2 = boto3.client('ec2')
        self.cloudwatch = boto3.client('cloudwatch')
        self.rds = boto3.client('rds')
        self.elbv2 = boto3.client('elbv2')
        
    def detect_idle_ec2_instances(self, days: int = 7) -> List[Dict]:
        """Detect idle EC2 instances based on CloudWatch metrics"""
        idle_instances = []
        
        # Get all running instances
        instances = self.ec2.describe_instances(
            Filters=[{'Name': 'instance-state-name', 'Values': ['running']}]
        )
        
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)
        
        for reservation in instances['Reservations']:
            for instance in reservation['Instances']:
                instance_id = instance['InstanceId']
                
                # Check CPU utilization
                cpu_metrics = self.cloudwatch.get_metric_statistics(
                    Namespace='AWS/EC2',
                    MetricName='CPUUtilization',
                    Dimensions=[{'Name': 'InstanceId', 'Value': instance_id}],
                    StartTime=start_time,
                    EndTime=end_time,
                    Period=3600,  # 1 hour
                    Statistics=['Average']
                )
                
                if cpu_metrics['Datapoints']:
                    avg_cpu = sum(dp['Average'] for dp in cpu_metrics['Datapoints']) / len(cpu_metrics['Datapoints'])
                    
                    if avg_cpu < 5.0:  # Less than 5% CPU
                        idle_instances.append({
                            'InstanceId': instance_id,
                            'InstanceType': instance['InstanceType'],
                            'LaunchTime': instance['LaunchTime'].isoformat(),
                            'AvgCPU': round(avg_cpu, 2),
                            'Tags': instance.get('Tags', [])
                        })
        
        return idle_instances
    
    def detect_unattached_ebs_volumes(self) -> List[Dict]:
        """Detect unattached EBS volumes"""
        unattached_volumes = []
        
        volumes = self.ec2.describe_volumes(
            Filters=[{'Name': 'status', 'Values': ['available']}]
        )
        
        for volume in volumes['Volumes']:
            # Calculate monthly cost estimate
            size_gb = volume['Size']
            volume_type = volume['VolumeType']
            
            # Rough cost calculation (varies by region)
            cost_per_gb = {
                'gp2': 0.10, 'gp3': 0.08, 'io1': 0.125, 'io2': 0.125,
                'st1': 0.045, 'sc1': 0.025, 'standard': 0.05
            }
            
            monthly_cost = size_gb * cost_per_gb.get(volume_type, 0.10)
            
            unattached_volumes.append({
                'VolumeId': volume['VolumeId'],
                'Size': size_gb,
                'VolumeType': volume_type,
                'CreateTime': volume['CreateTime'].isoformat(),
                'EstimatedMonthlyCost': round(monthly_cost, 2),
                'Tags': volume.get('Tags', [])
            })
        
        return unattached_volumes
    
    def detect_idle_load_balancers(self, days: int = 7) -> List[Dict]:
        """Detect idle Application Load Balancers"""
        idle_albs = []
        
        load_balancers = self.elbv2.describe_load_balancers()
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days)
        
        for lb in load_balancers['LoadBalancers']:
            lb_arn = lb['LoadBalancerArn']
            lb_name = lb['LoadBalancerName']
            
            # Check request count
            request_metrics = self.cloudwatch.get_metric_statistics(
                Namespace='AWS/ApplicationELB',
                MetricName='RequestCount',
                Dimensions=[{'Name': 'LoadBalancer', 'Value': lb_name}],
                StartTime=start_time,
                EndTime=end_time,
                Period=3600,
                Statistics=['Sum']
            )
            
            total_requests = sum(dp['Sum'] for dp in request_metrics['Datapoints'])
            
            if total_requests == 0:
                idle_albs.append({
                    'LoadBalancerArn': lb_arn,
                    'LoadBalancerName': lb_name,
                    'Type': lb['Type'],
                    'CreatedTime': lb['CreatedTime'].isoformat(),
                    'TotalRequests': total_requests
                })
        
        return idle_albs
    
    def generate_cleanup_report(self) -> Dict:
        """Generate comprehensive idle resource report"""
        report = {
            'timestamp': datetime.utcnow().isoformat(),
            'idle_ec2_instances': self.detect_idle_ec2_instances(),
            'unattached_ebs_volumes': self.detect_unattached_ebs_volumes(),
            'idle_load_balancers': self.detect_idle_load_balancers()
        }
        
        # Calculate total potential savings
        total_savings = 0
        
        # EBS volume savings
        for volume in report['unattached_ebs_volumes']:
            total_savings += volume['EstimatedMonthlyCost']
        
        report['estimated_monthly_savings'] = round(total_savings, 2)
        
        return report

# Usage example
if __name__ == "__main__":
    detector = IdleResourceDetector()
    report = detector.generate_cleanup_report()
    
    print(json.dumps(report, indent=2, default=str))
```

## 🤖 Automated Cleanup Strategies

### Scheduled Cleanup Lambda
```python
import boto3
import json
from datetime import datetime, timedelta

def lambda_handler(event, context):
    """
    Automated cleanup of idle resources
    Runs on a schedule via EventBridge
    """
    
    ec2 = boto3.client('ec2')
    sns = boto3.client('sns')
    
    cleanup_actions = []
    
    # 1. Delete unattached EBS volumes older than 30 days
    volumes = ec2.describe_volumes(
        Filters=[
            {'Name': 'status', 'Values': ['available']},
            {'Name': 'tag:AutoCleanup', 'Values': ['enabled']}
        ]
    )
    
    cutoff_date = datetime.utcnow() - timedelta(days=30)
    
    for volume in volumes['Volumes']:
        if volume['CreateTime'].replace(tzinfo=None) < cutoff_date:
            try:
                ec2.delete_volume(VolumeId=volume['VolumeId'])
                cleanup_actions.append(f"Deleted volume: {volume['VolumeId']}")
            except Exception as e:
                cleanup_actions.append(f"Failed to delete volume {volume['VolumeId']}: {str(e)}")
    
    # 2. Stop idle EC2 instances (tagged for auto-shutdown)
    instances = ec2.describe_instances(
        Filters=[
            {'Name': 'instance-state-name', 'Values': ['running']},
            {'Name': 'tag:AutoShutdown', 'Values': ['enabled']}
        ]
    )
    
    for reservation in instances['Reservations']:
        for instance in reservation['Instances']:
            # Check if instance should be stopped based on schedule
            schedule_tag = next((tag['Value'] for tag in instance.get('Tags', []) 
                               if tag['Key'] == 'Schedule'), None)
            
            if schedule_tag == 'business-hours':
                current_hour = datetime.utcnow().hour
                if current_hour < 8 or current_hour > 18:  # Outside business hours
                    try:
                        ec2.stop_instances(InstanceIds=[instance['InstanceId']])
                        cleanup_actions.append(f"Stopped instance: {instance['InstanceId']}")
                    except Exception as e:
                        cleanup_actions.append(f"Failed to stop instance {instance['InstanceId']}: {str(e)}")
    
    # 3. Delete old snapshots (older than 90 days, not used by AMIs)
    snapshots = ec2.describe_snapshots(OwnerIds=['self'])
    
    for snapshot in snapshots['Snapshots']:
        if snapshot['StartTime'].replace(tzinfo=None) < datetime.utcnow() - timedelta(days=90):
            # Check if snapshot is used by any AMI
            images = ec2.describe_images(
                Owners=['self'],
                Filters=[{'Name': 'block-device-mapping.snapshot-id', 'Values': [snapshot['SnapshotId']]}]
            )
            
            if not images['Images']:  # Not used by any AMI
                try:
                    ec2.delete_snapshot(SnapshotId=snapshot['SnapshotId'])
                    cleanup_actions.append(f"Deleted snapshot: {snapshot['SnapshotId']}")
                except Exception as e:
                    cleanup_actions.append(f"Failed to delete snapshot {snapshot['SnapshotId']}: {str(e)}")
    
    # Send notification
    if cleanup_actions:
        message = "Automated cleanup completed:\n\n" + "\n".join(cleanup_actions)
        sns.publish(
            TopicArn='arn:aws:sns:us-east-1:123456789012:cleanup-notifications',
            Subject='AWS Resource Cleanup Report',
            Message=message
        )
    
    return {
        'statusCode': 200,
        'body': json.dumps({
            'message': 'Cleanup completed',
            'actions': cleanup_actions
        })
    }
```

### Terraform Configuration for Cleanup Automation
```hcl
# EventBridge rule for daily cleanup
resource "aws_cloudwatch_event_rule" "daily_cleanup" {
  name                = "daily-resource-cleanup"
  description         = "Trigger daily cleanup of idle resources"
  schedule_expression = "cron(0 2 * * ? *)"  # 2 AM daily
}

resource "aws_cloudwatch_event_target" "cleanup_lambda" {
  rule      = aws_cloudwatch_event_rule.daily_cleanup.name
  target_id = "CleanupLambdaTarget"
  arn       = aws_lambda_function.resource_cleanup.arn
}

# Lambda function for cleanup
resource "aws_lambda_function" "resource_cleanup" {
  filename         = "cleanup_function.zip"
  function_name    = "automated-resource-cleanup"
  role            = aws_iam_role.cleanup_lambda_role.arn
  handler         = "index.lambda_handler"
  runtime         = "python3.9"
  timeout         = 300

  environment {
    variables = {
      SNS_TOPIC_ARN = aws_sns_topic.cleanup_notifications.arn
    }
  }
}

# IAM role for cleanup Lambda
resource "aws_iam_role" "cleanup_lambda_role" {
  name = "cleanup-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "cleanup_lambda_policy" {
  name = "cleanup-lambda-policy"
  role = aws_iam_role.cleanup_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ec2:DescribeInstances",
          "ec2:DescribeVolumes",
          "ec2:DescribeSnapshots",
          "ec2:DescribeImages",
          "ec2:StopInstances",
          "ec2:DeleteVolume",
          "ec2:DeleteSnapshot",
          "sns:Publish"
        ]
        Resource = "*"
      }
    ]
  })
}
```

## 📊 CUR Query Examples

### Unattached EBS Volumes Query
```sql
-- Find unattached EBS volumes and their costs
SELECT 
  line_item_resource_id,
  product_volume_type,
  line_item_usage_amount,
  line_item_unblended_cost,
  line_item_usage_start_date
FROM cur_table
WHERE 
  line_item_product_code = 'AmazonEC2'
  AND line_item_usage_type LIKE '%EBS%'
  AND line_item_resource_id NOT IN (
    SELECT DISTINCT line_item_resource_id 
    FROM cur_table 
    WHERE line_item_usage_type LIKE '%Instance%'
  )
  AND year = '2024'
  AND month = '01'
ORDER BY line_item_unblended_cost DESC;
```

### Idle EC2 Instances Query
```sql
-- Find potentially idle EC2 instances (low usage)
WITH instance_usage AS (
  SELECT 
    line_item_resource_id,
    product_instance_type,
    SUM(line_item_usage_amount) as total_hours,
    SUM(line_item_unblended_cost) as total_cost,
    COUNT(DISTINCT line_item_usage_start_date) as usage_days
  FROM cur_table
  WHERE 
    line_item_product_code = 'AmazonEC2'
    AND line_item_usage_type LIKE '%Instance%'
    AND year = '2024'
    AND month = '01'
  GROUP BY line_item_resource_id, product_instance_type
)
SELECT 
  line_item_resource_id,
  product_instance_type,
  total_hours,
  total_cost,
  usage_days,
  (total_hours / (usage_days * 24)) as utilization_percentage
FROM instance_usage
WHERE (total_hours / (usage_days * 24)) < 0.1  -- Less than 10% utilization
ORDER BY total_cost DESC;
```

### Unused Load Balancers Query
```sql
-- Find load balancers with no data transfer
SELECT 
  line_item_resource_id,
  product_load_balancer_type,
  SUM(line_item_usage_amount) as total_usage,
  SUM(line_item_unblended_cost) as total_cost
FROM cur_table
WHERE 
  line_item_product_code = 'AWSELB'
  AND line_item_usage_type LIKE '%DataTransfer%'
  AND year = '2024'
  AND month = '01'
GROUP BY line_item_resource_id, product_load_balancer_type
HAVING SUM(line_item_usage_amount) = 0
ORDER BY total_cost DESC;
```

## 🛠️ Tools and Integration

### Free Tools

#### AWS Compute Optimizer
- **Rightsizing recommendations** for EC2, EBS, Lambda
- **Utilization metrics** and analysis
- **Cost optimization** opportunities
- **Performance risk** assessment

#### AWS Trusted Advisor
- **Idle resource detection** (Premium support required)
- **Cost optimization** recommendations
- **Security and performance** insights
- **Service limit** monitoring

### Premium Tools

#### nOps
- **Automated resource** scheduling and cleanup
- **ML-powered** idle resource detection
- **Cost optimization** recommendations
- **Automated rightsizing**

#### CloudFix
- **Automated cost** optimization fixes
- **Risk-free** optimization recommendations
- **Continuous monitoring** and optimization
- **ROI tracking** and reporting

### Tool Comparison
```yaml
tool_comparison:
  aws_compute_optimizer:
    cost: "Free"
    coverage: "EC2, EBS, Lambda, Auto Scaling"
    automation: "Recommendations only"
    
  aws_trusted_advisor:
    cost: "Free (basic) / Premium (advanced)"
    coverage: "Multi-service"
    automation: "Recommendations only"
    
  nops:
    cost: "Usage-based pricing"
    coverage: "Comprehensive"
    automation: "Full automation available"
    
  cloudfix:
    cost: "Percentage of savings"
    coverage: "Multi-service"
    automation: "Automated fixes"
```

## ✅ Study Checkpoints

### Knowledge Check
- [ ] Can you identify different types of idle resources?
- [ ] Do you understand automated cleanup strategies?
- [ ] Can you write CUR queries for cost optimization?
- [ ] Do you know when to use different tools?

### Practical Skills
- [ ] Can you implement idle resource detection scripts?
- [ ] Do you know how to set up automated cleanup?
- [ ] Can you create cost optimization reports?
- [ ] Do you understand the risks of automated cleanup?

## 📋 Practical Examples

See the `examples/` directory for:
- Complete idle resource detection scripts
- Automated cleanup Lambda functions
- CUR query library for optimization
- Monitoring and alerting configurations

## ➡️ Next Steps
Continue to [Module 06: Spot Instances and Fleet Optimization](../06_spot_instances_and_fleet_optimization/)

---
**Module Duration**: 1 week  
**Difficulty**: Intermediate  
**Prerequisites**: Module 04 completion
